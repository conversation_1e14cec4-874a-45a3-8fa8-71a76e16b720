# Docker CE 26.1.4 CentOS 7 离线安装依赖包清单

## 已有的包
✅ docker-ce-cli-26.1.4-1.el7.x86_64.rpm (15MB)
✅ docker-ce-26.1.4-1.el7.x86_64.rpm (27MB)  
✅ containerd.io-1.6.9-3.1.el7.x86_64.rpm (33MB)

## 缺少的依赖包 - 需要下载

### 核心依赖
```bash
# SELinux相关
wget http://mirror.centos.org/centos/7/extras/x86_64/Packages/container-selinux-2.119.2-1.911c772.el7_8.noarch.rpm

# 系统库
wget http://mirror.centos.org/centos/7/os/x86_64/Packages/libcgroup-0.41-21.el7.x86_64.rpm
wget http://mirror.centos.org/centos/7/os/x86_64/Packages/libseccomp-2.3.1-4.el7.x86_64.rpm

# 策略管理工具
wget http://mirror.centos.org/centos/7/os/x86_64/Packages/policycoreutils-python-2.5-34.el7.x86_64.rpm
wget http://mirror.centos.org/centos/7/os/x86_64/Packages/audit-libs-python-2.8.5-4.el7.x86_64.rpm
wget http://mirror.centos.org/centos/7/os/x86_64/Packages/checkpolicy-2.5-8.el7.x86_64.rpm
wget http://mirror.centos.org/centos/7/os/x86_64/Packages/libsemanage-python-2.5-14.el7.x86_64.rpm
wget http://mirror.centos.org/centos/7/os/x86_64/Packages/setools-libs-3.3.8-4.el7.x86_64.rpm
wget http://mirror.centos.org/centos/7/os/x86_64/Packages/python-IPy-0.75-6.el7.noarch.rpm

# 可能需要的附加依赖
wget http://mirror.centos.org/centos/7/os/x86_64/Packages/policycoreutils-2.5-34.el7.x86_64.rpm
wget http://mirror.centos.org/centos/7/os/x86_64/Packages/libselinux-python-2.5-15.el7.x86_64.rpm
```

### Docker Compose (可选)
```bash
# 如果需要Docker Compose
wget https://github.com/docker/compose/releases/download/v2.21.0/docker-compose-linux-x86_64
```

## 下载脚本
```bash
#!/bin/bash
# 创建下载目录
mkdir -p docker-dependencies
cd docker-dependencies

# 下载所有依赖包
wget http://mirror.centos.org/centos/7/extras/x86_64/Packages/container-selinux-2.119.2-1.911c772.el7_8.noarch.rpm
wget http://mirror.centos.org/centos/7/os/x86_64/Packages/libcgroup-0.41-21.el7.x86_64.rpm
wget http://mirror.centos.org/centos/7/os/x86_64/Packages/libseccomp-2.3.1-4.el7.x86_64.rpm
wget http://mirror.centos.org/centos/7/os/x86_64/Packages/policycoreutils-python-2.5-34.el7.x86_64.rpm
wget http://mirror.centos.org/centos/7/os/x86_64/Packages/audit-libs-python-2.8.5-4.el7.x86_64.rpm
wget http://mirror.centos.org/centos/7/os/x86_64/Packages/checkpolicy-2.5-8.el7.x86_64.rpm
wget http://mirror.centos.org/centos/7/os/x86_64/Packages/libsemanage-python-2.5-14.el7.x86_64.rpm
wget http://mirror.centos.org/centos/7/os/x86_64/Packages/setools-libs-3.3.8-4.el7.x86_64.rpm
wget http://mirror.centos.org/centos/7/os/x86_64/Packages/python-IPy-0.75-6.el7.noarch.rpm
wget http://mirror.centos.org/centos/7/os/x86_64/Packages/policycoreutils-2.5-34.el7.x86_64.rpm
wget http://mirror.centos.org/centos/7/os/x86_64/Packages/libselinux-python-2.5-15.el7.x86_64.rpm

echo "所有依赖包下载完成！"
```

## 安装顺序
```bash
# 在目标CentOS 7系统上的安装顺序：
1. rpm -ivh libcgroup-0.41-21.el7.x86_64.rpm
2. rpm -ivh libseccomp-2.3.1-4.el7.x86_64.rpm
3. rpm -ivh setools-libs-3.3.8-4.el7.x86_64.rpm
4. rpm -ivh python-IPy-0.75-6.el7.noarch.rpm
5. rpm -ivh libselinux-python-2.5-15.el7.x86_64.rpm
6. rpm -ivh libsemanage-python-2.5-14.el7.x86_64.rpm
7. rpm -ivh audit-libs-python-2.8.5-4.el7.x86_64.rpm
8. rpm -ivh checkpolicy-2.5-8.el7.x86_64.rpm
9. rpm -ivh policycoreutils-2.5-34.el7.x86_64.rpm
10. rpm -ivh policycoreutils-python-2.5-34.el7.x86_64.rpm
11. rpm -ivh container-selinux-2.119.2-1.911c772.el7_8.noarch.rpm
12. rpm -ivh containerd.io-1.6.9-3.1.el7.x86_64.rpm
13. rpm -ivh docker-ce-cli-26.1.4-1.el7.x86_64.rpm
14. rpm -ivh docker-ce-26.1.4-1.el7.x86_64.rpm
```

## 验证安装
```bash
systemctl enable docker
systemctl start docker
docker --version
docker run hello-world
``` 