#!/bin/bash
# JIRA备份脚本

DATE=$(date +%Y%m%d_%H%M%S)
BACKUP_DIR="./backups"

echo "开始备份 JIRA ($DATE)..."

# 创建备份目录
mkdir -p "$BACKUP_DIR"

# 备份MySQL数据库
echo "Backing up MySQL database..."
docker exec jira-mysql mysqladmin ping -u root --password="iie.ac.cn"
if [ $? -eq 0 ]; then
    docker exec jira-mysql mysqldump -u root --password="iie.ac.cn" jiradb > "$BACKUP_DIR/jira-mysql-$DATE.sql"
    echo "MySQL backup completed"
else
    echo "MySQL backup failed - database not accessible"
    exit 1
fi

# 备份JIRA数据目录
echo "Backing up JIRA data..."
tar -czf "$BACKUP_DIR/jira-data-$DATE.tar.gz" -C data jira-data/
echo "JIRA data backup completed"

echo "备份完成:"
ls -lh "$BACKUP_DIR/"*$DATE* 

# echo "Backup files saved to: $BACKUP_DIR/" 

# ```bash
# # 编辑crontab
# crontab -e

# # 添加以下行（每天凌晨2点备份）
# 0 2 * * * cd /opt/jira-project && bash backup.sh

# # 每周日凌晨3点清理30天前的备份
# 0 3 * * 0 find /opt/jira-project/backups -name "*.sql" -mtime +30 -delete
# 0 3 * * 0 find /opt/jira-project/backups -name "*.tar.gz" -mtime +30 -delete
# ```