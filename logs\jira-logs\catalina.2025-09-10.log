10-Sep-2025 13:19:28.234 WARNING [main] org.apache.catalina.startup.SetAllPropertiesRule.begin [SetAllPropertiesRule]{Server/Service/Connector} Setting property 'proxyPort' to '' did not find a matching property.
10-Sep-2025 13:19:28.276 INFO [main] org.apache.catalina.startup.VersionLoggerListener.log Server version:        Apache Tomcat/8.5.42
10-Sep-2025 13:19:28.276 INFO [main] org.apache.catalina.startup.VersionLoggerListener.log Server built:          Jun 4 2019 20:29:04 UTC
10-Sep-2025 13:19:28.277 INFO [main] org.apache.catalina.startup.VersionLoggerListener.log Server number:         8.5.42.0
10-Sep-2025 13:19:28.277 INFO [main] org.apache.catalina.startup.VersionLoggerListener.log OS Name:               Linux
10-Sep-2025 13:19:28.277 INFO [main] org.apache.catalina.startup.VersionLoggerListener.log OS Version:            6.6.87.2-microsoft-standard-WSL2
10-Sep-2025 13:19:28.277 INFO [main] org.apache.catalina.startup.VersionLoggerListener.log Architecture:          amd64
10-Sep-2025 13:19:28.277 INFO [main] org.apache.catalina.startup.VersionLoggerListener.log Java Home:             /opt/java/openjdk
10-Sep-2025 13:19:28.277 INFO [main] org.apache.catalina.startup.VersionLoggerListener.log JVM Version:           11.0.15+10
10-Sep-2025 13:19:28.277 INFO [main] org.apache.catalina.startup.VersionLoggerListener.log JVM Vendor:            Eclipse Adoptium
10-Sep-2025 13:19:28.277 INFO [main] org.apache.catalina.startup.VersionLoggerListener.log CATALINA_BASE:         /opt/atlassian/jira
10-Sep-2025 13:19:28.277 INFO [main] org.apache.catalina.startup.VersionLoggerListener.log CATALINA_HOME:         /opt/atlassian/jira
10-Sep-2025 13:19:28.277 INFO [main] org.apache.catalina.startup.VersionLoggerListener.log Command line argument: --add-opens=java.base/java.lang=ALL-UNNAMED
10-Sep-2025 13:19:28.277 INFO [main] org.apache.catalina.startup.VersionLoggerListener.log Command line argument: --add-opens=java.base/java.io=ALL-UNNAMED
10-Sep-2025 13:19:28.277 INFO [main] org.apache.catalina.startup.VersionLoggerListener.log Command line argument: --add-opens=java.rmi/sun.rmi.transport=ALL-UNNAMED
10-Sep-2025 13:19:28.278 INFO [main] org.apache.catalina.startup.VersionLoggerListener.log Command line argument: -Djava.util.logging.config.file=/opt/atlassian/jira/conf/logging.properties
10-Sep-2025 13:19:28.278 INFO [main] org.apache.catalina.startup.VersionLoggerListener.log Command line argument: -Djava.util.logging.manager=org.apache.juli.ClassLoaderLogManager
10-Sep-2025 13:19:28.278 INFO [main] org.apache.catalina.startup.VersionLoggerListener.log Command line argument: -Xms1024m
10-Sep-2025 13:19:28.278 INFO [main] org.apache.catalina.startup.VersionLoggerListener.log Command line argument: -Xmx2048m
10-Sep-2025 13:19:28.278 INFO [main] org.apache.catalina.startup.VersionLoggerListener.log Command line argument: -XX:InitialCodeCacheSize=32m
10-Sep-2025 13:19:28.278 INFO [main] org.apache.catalina.startup.VersionLoggerListener.log Command line argument: -XX:ReservedCodeCacheSize=512m
10-Sep-2025 13:19:28.278 INFO [main] org.apache.catalina.startup.VersionLoggerListener.log Command line argument: -Djava.awt.headless=true
10-Sep-2025 13:19:28.278 INFO [main] org.apache.catalina.startup.VersionLoggerListener.log Command line argument: -Datlassian.standalone=JIRA
10-Sep-2025 13:19:28.278 INFO [main] org.apache.catalina.startup.VersionLoggerListener.log Command line argument: -Dorg.apache.jasper.runtime.BodyContentImpl.LIMIT_BUFFER=true
10-Sep-2025 13:19:28.278 INFO [main] org.apache.catalina.startup.VersionLoggerListener.log Command line argument: -Dmail.mime.decodeparameters=true
10-Sep-2025 13:19:28.278 INFO [main] org.apache.catalina.startup.VersionLoggerListener.log Command line argument: -Dorg.dom4j.factory=com.atlassian.core.xml.InterningDocumentFactory
10-Sep-2025 13:19:28.278 INFO [main] org.apache.catalina.startup.VersionLoggerListener.log Command line argument: -XX:-OmitStackTraceInFastThrow
10-Sep-2025 13:19:28.278 INFO [main] org.apache.catalina.startup.VersionLoggerListener.log Command line argument: -Djava.locale.providers=COMPAT
10-Sep-2025 13:19:28.278 INFO [main] org.apache.catalina.startup.VersionLoggerListener.log Command line argument: -Djira.home=/var/atlassian/application-data/jira
10-Sep-2025 13:19:28.278 INFO [main] org.apache.catalina.startup.VersionLoggerListener.log Command line argument: -Datlassian.plugins.startup.options=-fg
10-Sep-2025 13:19:28.278 INFO [main] org.apache.catalina.startup.VersionLoggerListener.log Command line argument: -Djdk.tls.ephemeralDHKeySize=2048
10-Sep-2025 13:19:28.279 INFO [main] org.apache.catalina.startup.VersionLoggerListener.log Command line argument: -Djava.protocol.handler.pkgs=org.apache.catalina.webresources
10-Sep-2025 13:19:28.279 INFO [main] org.apache.catalina.startup.VersionLoggerListener.log Command line argument: -Dorg.apache.catalina.security.SecurityListener.UMASK=0027
10-Sep-2025 13:19:28.279 INFO [main] org.apache.catalina.startup.VersionLoggerListener.log Command line argument: -Xlog:gc*:file=/opt/atlassian/jira/logs/atlassian-jira-gc-%t.log:time,uptime:filecount=5,filesize=20M
10-Sep-2025 13:19:28.279 INFO [main] org.apache.catalina.startup.VersionLoggerListener.log Command line argument: -XX:+ExplicitGCInvokesConcurrent
10-Sep-2025 13:19:28.279 INFO [main] org.apache.catalina.startup.VersionLoggerListener.log Command line argument: -Dignore.endorsed.dirs=
10-Sep-2025 13:19:28.279 INFO [main] org.apache.catalina.startup.VersionLoggerListener.log Command line argument: -Dcatalina.base=/opt/atlassian/jira
10-Sep-2025 13:19:28.279 INFO [main] org.apache.catalina.startup.VersionLoggerListener.log Command line argument: -Dcatalina.home=/opt/atlassian/jira
10-Sep-2025 13:19:28.279 INFO [main] org.apache.catalina.startup.VersionLoggerListener.log Command line argument: -Djava.io.tmpdir=/opt/atlassian/jira/temp
10-Sep-2025 13:19:28.279 INFO [main] org.apache.catalina.core.AprLifecycleListener.lifecycleEvent The APR based Apache Tomcat Native library which allows optimal performance in production environments was not found on the java.library.path: [/usr/java/packages/lib:/usr/lib64:/lib64:/lib:/usr/lib]
10-Sep-2025 13:19:28.337 INFO [main] org.apache.coyote.AbstractProtocol.init Initializing ProtocolHandler ["http-nio-8080"]
10-Sep-2025 13:19:28.346 INFO [main] org.apache.catalina.startup.Catalina.load Initialization processed in 379 ms
10-Sep-2025 13:19:28.369 INFO [main] org.apache.catalina.core.StandardService.startInternal Starting service [Catalina]
10-Sep-2025 13:19:28.369 INFO [main] org.apache.catalina.core.StandardEngine.startInternal Starting Servlet Engine: Apache Tomcat/8.5.42
10-Sep-2025 13:19:33.898 INFO [main] org.apache.coyote.AbstractProtocol.start Starting ProtocolHandler ["http-nio-8080"]
10-Sep-2025 13:19:33.906 INFO [main] org.apache.tomcat.util.net.NioSelectorPool.getSharedSelector Using a shared selector for servlet write/read
10-Sep-2025 13:19:33.916 INFO [main] org.apache.catalina.startup.Catalina.start Server startup in 5570 ms
