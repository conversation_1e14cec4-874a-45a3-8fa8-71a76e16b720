10-Sep-2025 14:00:33.711 WARNING [main] org.apache.catalina.startup.SetAllPropertiesRule.begin [SetAllPropertiesRule]{Server/Service/Connector} Setting property 'proxyPort' to '' did not find a matching property.
10-Sep-2025 14:00:33.757 INFO [main] org.apache.catalina.startup.VersionLoggerListener.log Server version:        Apache Tomcat/8.5.42
10-Sep-2025 14:00:33.758 INFO [main] org.apache.catalina.startup.VersionLoggerListener.log Server built:          Jun 4 2019 20:29:04 UTC
10-Sep-2025 14:00:33.758 INFO [main] org.apache.catalina.startup.VersionLoggerListener.log Server number:         8.5.42.0
10-Sep-2025 14:00:33.758 INFO [main] org.apache.catalina.startup.VersionLoggerListener.log OS Name:               Linux
10-Sep-2025 14:00:33.758 INFO [main] org.apache.catalina.startup.VersionLoggerListener.log OS Version:            6.6.87.2-microsoft-standard-WSL2
10-Sep-2025 14:00:33.758 INFO [main] org.apache.catalina.startup.VersionLoggerListener.log Architecture:          amd64
10-Sep-2025 14:00:33.758 INFO [main] org.apache.catalina.startup.VersionLoggerListener.log Java Home:             /opt/java/openjdk
10-Sep-2025 14:00:33.758 INFO [main] org.apache.catalina.startup.VersionLoggerListener.log JVM Version:           11.0.15+10
10-Sep-2025 14:00:33.758 INFO [main] org.apache.catalina.startup.VersionLoggerListener.log JVM Vendor:            Eclipse Adoptium
10-Sep-2025 14:00:33.758 INFO [main] org.apache.catalina.startup.VersionLoggerListener.log CATALINA_BASE:         /opt/atlassian/jira
10-Sep-2025 14:00:33.758 INFO [main] org.apache.catalina.startup.VersionLoggerListener.log CATALINA_HOME:         /opt/atlassian/jira
10-Sep-2025 14:00:33.759 INFO [main] org.apache.catalina.startup.VersionLoggerListener.log Command line argument: --add-opens=java.base/java.lang=ALL-UNNAMED
10-Sep-2025 14:00:33.759 INFO [main] org.apache.catalina.startup.VersionLoggerListener.log Command line argument: --add-opens=java.base/java.io=ALL-UNNAMED
10-Sep-2025 14:00:33.759 INFO [main] org.apache.catalina.startup.VersionLoggerListener.log Command line argument: --add-opens=java.rmi/sun.rmi.transport=ALL-UNNAMED
10-Sep-2025 14:00:33.759 INFO [main] org.apache.catalina.startup.VersionLoggerListener.log Command line argument: -Djava.util.logging.config.file=/opt/atlassian/jira/conf/logging.properties
10-Sep-2025 14:00:33.759 INFO [main] org.apache.catalina.startup.VersionLoggerListener.log Command line argument: -Djava.util.logging.manager=org.apache.juli.ClassLoaderLogManager
10-Sep-2025 14:00:33.759 INFO [main] org.apache.catalina.startup.VersionLoggerListener.log Command line argument: -Xms1024m
10-Sep-2025 14:00:33.759 INFO [main] org.apache.catalina.startup.VersionLoggerListener.log Command line argument: -Xmx2048m
10-Sep-2025 14:00:33.759 INFO [main] org.apache.catalina.startup.VersionLoggerListener.log Command line argument: -XX:InitialCodeCacheSize=32m
10-Sep-2025 14:00:33.759 INFO [main] org.apache.catalina.startup.VersionLoggerListener.log Command line argument: -XX:ReservedCodeCacheSize=512m
10-Sep-2025 14:00:33.759 INFO [main] org.apache.catalina.startup.VersionLoggerListener.log Command line argument: -Djava.awt.headless=true
10-Sep-2025 14:00:33.759 INFO [main] org.apache.catalina.startup.VersionLoggerListener.log Command line argument: -Datlassian.standalone=JIRA
10-Sep-2025 14:00:33.759 INFO [main] org.apache.catalina.startup.VersionLoggerListener.log Command line argument: -Dorg.apache.jasper.runtime.BodyContentImpl.LIMIT_BUFFER=true
10-Sep-2025 14:00:33.759 INFO [main] org.apache.catalina.startup.VersionLoggerListener.log Command line argument: -Dmail.mime.decodeparameters=true
10-Sep-2025 14:00:33.759 INFO [main] org.apache.catalina.startup.VersionLoggerListener.log Command line argument: -Dorg.dom4j.factory=com.atlassian.core.xml.InterningDocumentFactory
10-Sep-2025 14:00:33.760 INFO [main] org.apache.catalina.startup.VersionLoggerListener.log Command line argument: -XX:-OmitStackTraceInFastThrow
10-Sep-2025 14:00:33.760 INFO [main] org.apache.catalina.startup.VersionLoggerListener.log Command line argument: -Djava.locale.providers=COMPAT
10-Sep-2025 14:00:33.760 INFO [main] org.apache.catalina.startup.VersionLoggerListener.log Command line argument: -Djira.home=/var/atlassian/application-data/jira
10-Sep-2025 14:00:33.760 INFO [main] org.apache.catalina.startup.VersionLoggerListener.log Command line argument: -Datlassian.plugins.startup.options=-fg
10-Sep-2025 14:00:33.760 INFO [main] org.apache.catalina.startup.VersionLoggerListener.log Command line argument: -Djdk.tls.ephemeralDHKeySize=2048
10-Sep-2025 14:00:33.760 INFO [main] org.apache.catalina.startup.VersionLoggerListener.log Command line argument: -Djava.protocol.handler.pkgs=org.apache.catalina.webresources
10-Sep-2025 14:00:33.760 INFO [main] org.apache.catalina.startup.VersionLoggerListener.log Command line argument: -Dorg.apache.catalina.security.SecurityListener.UMASK=0027
10-Sep-2025 14:00:33.760 INFO [main] org.apache.catalina.startup.VersionLoggerListener.log Command line argument: -Xlog:gc*:file=/opt/atlassian/jira/logs/atlassian-jira-gc-%t.log:time,uptime:filecount=5,filesize=20M
10-Sep-2025 14:00:33.760 INFO [main] org.apache.catalina.startup.VersionLoggerListener.log Command line argument: -XX:+ExplicitGCInvokesConcurrent
10-Sep-2025 14:00:33.760 INFO [main] org.apache.catalina.startup.VersionLoggerListener.log Command line argument: -Dignore.endorsed.dirs=
10-Sep-2025 14:00:33.760 INFO [main] org.apache.catalina.startup.VersionLoggerListener.log Command line argument: -Dcatalina.base=/opt/atlassian/jira
10-Sep-2025 14:00:33.760 INFO [main] org.apache.catalina.startup.VersionLoggerListener.log Command line argument: -Dcatalina.home=/opt/atlassian/jira
10-Sep-2025 14:00:33.760 INFO [main] org.apache.catalina.startup.VersionLoggerListener.log Command line argument: -Djava.io.tmpdir=/opt/atlassian/jira/temp
10-Sep-2025 14:00:33.760 INFO [main] org.apache.catalina.core.AprLifecycleListener.lifecycleEvent The APR based Apache Tomcat Native library which allows optimal performance in production environments was not found on the java.library.path: [/usr/java/packages/lib:/usr/lib64:/lib64:/lib:/usr/lib]
10-Sep-2025 14:00:33.814 INFO [main] org.apache.coyote.AbstractProtocol.init Initializing ProtocolHandler ["http-nio-8080"]
10-Sep-2025 14:00:33.823 INFO [main] org.apache.catalina.startup.Catalina.load Initialization processed in 377 ms
10-Sep-2025 14:00:33.848 INFO [main] org.apache.catalina.core.StandardService.startInternal Starting service [Catalina]
10-Sep-2025 14:00:33.849 INFO [main] org.apache.catalina.core.StandardEngine.startInternal Starting Servlet Engine: Apache Tomcat/8.5.42
10-Sep-2025 14:00:37.842 INFO [main] org.apache.coyote.AbstractProtocol.start Starting ProtocolHandler ["http-nio-8080"]
10-Sep-2025 14:00:37.850 INFO [main] org.apache.tomcat.util.net.NioSelectorPool.getSharedSelector Using a shared selector for servlet write/read
10-Sep-2025 14:00:37.858 INFO [main] org.apache.catalina.startup.Catalina.start Server startup in 4034 ms
10-Sep-2025 14:17:19.270 INFO [Thread-3] org.apache.coyote.AbstractProtocol.pause Pausing ProtocolHandler ["http-nio-8080"]
10-Sep-2025 14:17:19.278 INFO [Thread-3] org.apache.catalina.core.StandardService.stopInternal Stopping service [Catalina]
10-Sep-2025 14:17:19.483 WARNING [localhost-startStop-2] org.apache.catalina.loader.WebappClassLoaderBase.clearReferencesThreads The web application [ROOT] appears to have started a thread named [plugin-transaction-0] but has failed to stop it. This is very likely to create a memory leak. Stack trace of thread:
 java.base@11.0.15/jdk.internal.misc.Unsafe.park(Native Method)
 java.base@11.0.15/java.util.concurrent.locks.LockSupport.parkNanos(LockSupport.java:234)
 java.base@11.0.15/java.util.concurrent.locks.AbstractQueuedSynchronizer$ConditionObject.awaitNanos(AbstractQueuedSynchronizer.java:2123)
 java.base@11.0.15/java.util.concurrent.ScheduledThreadPoolExecutor$DelayedWorkQueue.take(ScheduledThreadPoolExecutor.java:1182)
 java.base@11.0.15/java.util.concurrent.ScheduledThreadPoolExecutor$DelayedWorkQueue.take(ScheduledThreadPoolExecutor.java:899)
 java.base@11.0.15/java.util.concurrent.ThreadPoolExecutor.getTask(ThreadPoolExecutor.java:1054)
 java.base@11.0.15/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1114)
 java.base@11.0.15/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:628)
 java.base@11.0.15/java.lang.Thread.run(Thread.java:829)
10-Sep-2025 14:17:19.485 SEVERE [localhost-startStop-2] org.apache.catalina.loader.WebappClassLoaderBase.checkThreadLocalMapForLeaks The web application [ROOT] created a ThreadLocal with key of type [java.lang.ThreadLocal] (value [java.lang.ThreadLocal@7bd7e8b]) and a value of type [com.atlassian.jira.tenancy.JiraTenantImpl] (value [JiraTenantImpl{id='fakeTenantId'}]) but failed to remove it when the web application was stopped. Threads are going to be renewed over time to try and avoid a probable memory leak.
10-Sep-2025 14:17:19.486 SEVERE [localhost-startStop-2] org.apache.catalina.loader.WebappClassLoaderBase.checkThreadLocalMapForLeaks The web application [ROOT] created a ThreadLocal with key of type [webwork.action.ActionContext$1] (value [webwork.action.ActionContext$1@4cdae552]) and a value of type [webwork.action.ActionContext] (value [webwork.action.ActionContext@62e2a6eb]) but failed to remove it when the web application was stopped. Threads are going to be renewed over time to try and avoid a probable memory leak.
10-Sep-2025 14:17:19.488 SEVERE [localhost-startStop-2] org.apache.catalina.loader.WebappClassLoaderBase.checkThreadLocalMapForLeaks The web application [ROOT] created a ThreadLocal with key of type [org.picocontainer.DefaultPicoContainer.IntoThreadLocal] (value [org.picocontainer.DefaultPicoContainer$IntoThreadLocal@958e368]) and a value of type [java.lang.Class] (value [class org.picocontainer.ComponentAdapter$NOTHING]) but failed to remove it when the web application was stopped. Threads are going to be renewed over time to try and avoid a probable memory leak.
10-Sep-2025 14:17:19.488 SEVERE [localhost-startStop-2] org.apache.catalina.loader.WebappClassLoaderBase.checkThreadLocalMapForLeaks The web application [ROOT] created a ThreadLocal with key of type [java.lang.ThreadLocal] (value [java.lang.ThreadLocal@7bd7e8b]) and a value of type [com.atlassian.jira.tenancy.JiraTenantImpl] (value [JiraTenantImpl{id='fakeTenantId'}]) but failed to remove it when the web application was stopped. Threads are going to be renewed over time to try and avoid a probable memory leak.
10-Sep-2025 14:17:19.489 SEVERE [localhost-startStop-2] org.apache.catalina.loader.WebappClassLoaderBase.checkThreadLocalMapForLeaks The web application [ROOT] created a ThreadLocal with key of type [org.picocontainer.DefaultPicoContainer.IntoThreadLocal] (value [org.picocontainer.DefaultPicoContainer$IntoThreadLocal@958e368]) and a value of type [java.lang.Class] (value [class org.picocontainer.ComponentAdapter$NOTHING]) but failed to remove it when the web application was stopped. Threads are going to be renewed over time to try and avoid a probable memory leak.
10-Sep-2025 14:17:19.489 SEVERE [localhost-startStop-2] org.apache.catalina.loader.WebappClassLoaderBase.checkThreadLocalMapForLeaks The web application [ROOT] created a ThreadLocal with key of type [webwork.action.ActionContext$1] (value [webwork.action.ActionContext$1@4cdae552]) and a value of type [webwork.action.ActionContext] (value [webwork.action.ActionContext@560809ad]) but failed to remove it when the web application was stopped. Threads are going to be renewed over time to try and avoid a probable memory leak.
10-Sep-2025 14:17:19.489 SEVERE [localhost-startStop-2] org.apache.catalina.loader.WebappClassLoaderBase.checkThreadLocalMapForLeaks The web application [ROOT] created a ThreadLocal with key of type [java.lang.ThreadLocal] (value [java.lang.ThreadLocal@7bd7e8b]) and a value of type [com.atlassian.jira.tenancy.JiraTenantImpl] (value [JiraTenantImpl{id='fakeTenantId'}]) but failed to remove it when the web application was stopped. Threads are going to be renewed over time to try and avoid a probable memory leak.
10-Sep-2025 14:17:19.489 SEVERE [localhost-startStop-2] org.apache.catalina.loader.WebappClassLoaderBase.checkThreadLocalMapForLeaks The web application [ROOT] created a ThreadLocal with key of type [webwork.action.ActionContext$1] (value [webwork.action.ActionContext$1@4cdae552]) and a value of type [webwork.action.ActionContext] (value [webwork.action.ActionContext@7e679309]) but failed to remove it when the web application was stopped. Threads are going to be renewed over time to try and avoid a probable memory leak.
10-Sep-2025 14:17:19.490 SEVERE [localhost-startStop-2] org.apache.catalina.loader.WebappClassLoaderBase.checkThreadLocalMapForLeaks The web application [ROOT] created a ThreadLocal with key of type [org.picocontainer.DefaultPicoContainer.IntoThreadLocal] (value [org.picocontainer.DefaultPicoContainer$IntoThreadLocal@958e368]) and a value of type [java.lang.Class] (value [class org.picocontainer.ComponentAdapter$NOTHING]) but failed to remove it when the web application was stopped. Threads are going to be renewed over time to try and avoid a probable memory leak.
10-Sep-2025 14:17:19.490 SEVERE [localhost-startStop-2] org.apache.catalina.loader.WebappClassLoaderBase.checkThreadLocalMapForLeaks The web application [ROOT] created a ThreadLocal with key of type [java.lang.ThreadLocal] (value [java.lang.ThreadLocal@7bd7e8b]) and a value of type [com.atlassian.jira.tenancy.JiraTenantImpl] (value [JiraTenantImpl{id='fakeTenantId'}]) but failed to remove it when the web application was stopped. Threads are going to be renewed over time to try and avoid a probable memory leak.
10-Sep-2025 14:17:19.490 SEVERE [localhost-startStop-2] org.apache.catalina.loader.WebappClassLoaderBase.checkThreadLocalMapForLeaks The web application [ROOT] created a ThreadLocal with key of type [webwork.action.ActionContext$1] (value [webwork.action.ActionContext$1@4cdae552]) and a value of type [webwork.action.ActionContext] (value [webwork.action.ActionContext@237965de]) but failed to remove it when the web application was stopped. Threads are going to be renewed over time to try and avoid a probable memory leak.
10-Sep-2025 14:17:19.490 SEVERE [localhost-startStop-2] org.apache.catalina.loader.WebappClassLoaderBase.checkThreadLocalMapForLeaks The web application [ROOT] created a ThreadLocal with key of type [org.picocontainer.DefaultPicoContainer.IntoThreadLocal] (value [org.picocontainer.DefaultPicoContainer$IntoThreadLocal@958e368]) and a value of type [java.lang.Class] (value [class org.picocontainer.ComponentAdapter$NOTHING]) but failed to remove it when the web application was stopped. Threads are going to be renewed over time to try and avoid a probable memory leak.
10-Sep-2025 14:17:19.490 SEVERE [localhost-startStop-2] org.apache.catalina.loader.WebappClassLoaderBase.checkThreadLocalMapForLeaks The web application [ROOT] created a ThreadLocal with key of type [java.lang.ThreadLocal] (value [java.lang.ThreadLocal@7bd7e8b]) and a value of type [com.atlassian.jira.tenancy.JiraTenantImpl] (value [JiraTenantImpl{id='fakeTenantId'}]) but failed to remove it when the web application was stopped. Threads are going to be renewed over time to try and avoid a probable memory leak.
10-Sep-2025 14:17:19.490 SEVERE [localhost-startStop-2] org.apache.catalina.loader.WebappClassLoaderBase.checkThreadLocalMapForLeaks The web application [ROOT] created a ThreadLocal with key of type [org.picocontainer.DefaultPicoContainer.IntoThreadLocal] (value [org.picocontainer.DefaultPicoContainer$IntoThreadLocal@958e368]) and a value of type [java.lang.Class] (value [class org.picocontainer.ComponentAdapter$NOTHING]) but failed to remove it when the web application was stopped. Threads are going to be renewed over time to try and avoid a probable memory leak.
10-Sep-2025 14:17:19.490 SEVERE [localhost-startStop-2] org.apache.catalina.loader.WebappClassLoaderBase.checkThreadLocalMapForLeaks The web application [ROOT] created a ThreadLocal with key of type [webwork.action.ActionContext$1] (value [webwork.action.ActionContext$1@4cdae552]) and a value of type [webwork.action.ActionContext] (value [webwork.action.ActionContext@4e4edaa6]) but failed to remove it when the web application was stopped. Threads are going to be renewed over time to try and avoid a probable memory leak.
10-Sep-2025 14:17:19.491 SEVERE [localhost-startStop-2] org.apache.catalina.loader.WebappClassLoaderBase.checkThreadLocalMapForLeaks The web application [ROOT] created a ThreadLocal with key of type [java.lang.ThreadLocal] (value [java.lang.ThreadLocal@7bd7e8b]) and a value of type [com.atlassian.jira.tenancy.JiraTenantImpl] (value [JiraTenantImpl{id='fakeTenantId'}]) but failed to remove it when the web application was stopped. Threads are going to be renewed over time to try and avoid a probable memory leak.
10-Sep-2025 14:17:19.491 SEVERE [localhost-startStop-2] org.apache.catalina.loader.WebappClassLoaderBase.checkThreadLocalMapForLeaks The web application [ROOT] created a ThreadLocal with key of type [webwork.action.ActionContext$1] (value [webwork.action.ActionContext$1@4cdae552]) and a value of type [webwork.action.ActionContext] (value [webwork.action.ActionContext@26f193b1]) but failed to remove it when the web application was stopped. Threads are going to be renewed over time to try and avoid a probable memory leak.
10-Sep-2025 14:17:19.491 SEVERE [localhost-startStop-2] org.apache.catalina.loader.WebappClassLoaderBase.checkThreadLocalMapForLeaks The web application [ROOT] created a ThreadLocal with key of type [org.picocontainer.DefaultPicoContainer.IntoThreadLocal] (value [org.picocontainer.DefaultPicoContainer$IntoThreadLocal@958e368]) and a value of type [java.lang.Class] (value [class org.picocontainer.ComponentAdapter$NOTHING]) but failed to remove it when the web application was stopped. Threads are going to be renewed over time to try and avoid a probable memory leak.
10-Sep-2025 14:17:19.491 SEVERE [localhost-startStop-2] org.apache.catalina.loader.WebappClassLoaderBase.checkThreadLocalMapForLeaks The web application [ROOT] created a ThreadLocal with key of type [java.lang.ThreadLocal] (value [java.lang.ThreadLocal@7bd7e8b]) and a value of type [com.atlassian.jira.tenancy.JiraTenantImpl] (value [JiraTenantImpl{id='fakeTenantId'}]) but failed to remove it when the web application was stopped. Threads are going to be renewed over time to try and avoid a probable memory leak.
10-Sep-2025 14:17:19.492 SEVERE [localhost-startStop-2] org.apache.catalina.loader.WebappClassLoaderBase.checkThreadLocalMapForLeaks The web application [ROOT] created a ThreadLocal with key of type [webwork.action.ActionContext$1] (value [webwork.action.ActionContext$1@4cdae552]) and a value of type [webwork.action.ActionContext] (value [webwork.action.ActionContext@2a1e73bc]) but failed to remove it when the web application was stopped. Threads are going to be renewed over time to try and avoid a probable memory leak.
10-Sep-2025 14:17:19.492 SEVERE [localhost-startStop-2] org.apache.catalina.loader.WebappClassLoaderBase.checkThreadLocalMapForLeaks The web application [ROOT] created a ThreadLocal with key of type [org.picocontainer.DefaultPicoContainer.IntoThreadLocal] (value [org.picocontainer.DefaultPicoContainer$IntoThreadLocal@958e368]) and a value of type [java.lang.Class] (value [class org.picocontainer.ComponentAdapter$NOTHING]) but failed to remove it when the web application was stopped. Threads are going to be renewed over time to try and avoid a probable memory leak.
10-Sep-2025 14:17:19.492 SEVERE [localhost-startStop-2] org.apache.catalina.loader.WebappClassLoaderBase.checkThreadLocalMapForLeaks The web application [ROOT] created a ThreadLocal with key of type [java.lang.ThreadLocal] (value [java.lang.ThreadLocal@7bd7e8b]) and a value of type [com.atlassian.jira.tenancy.JiraTenantImpl] (value [JiraTenantImpl{id='fakeTenantId'}]) but failed to remove it when the web application was stopped. Threads are going to be renewed over time to try and avoid a probable memory leak.
10-Sep-2025 14:17:19.493 SEVERE [localhost-startStop-2] org.apache.catalina.loader.WebappClassLoaderBase.checkThreadLocalMapForLeaks The web application [ROOT] created a ThreadLocal with key of type [webwork.action.ActionContext$1] (value [webwork.action.ActionContext$1@4cdae552]) and a value of type [webwork.action.ActionContext] (value [webwork.action.ActionContext@22ca15f0]) but failed to remove it when the web application was stopped. Threads are going to be renewed over time to try and avoid a probable memory leak.
10-Sep-2025 14:17:19.493 SEVERE [localhost-startStop-2] org.apache.catalina.loader.WebappClassLoaderBase.checkThreadLocalMapForLeaks The web application [ROOT] created a ThreadLocal with key of type [java.lang.ThreadLocal] (value [java.lang.ThreadLocal@7bd7e8b]) and a value of type [com.atlassian.jira.tenancy.JiraTenantImpl] (value [JiraTenantImpl{id='fakeTenantId'}]) but failed to remove it when the web application was stopped. Threads are going to be renewed over time to try and avoid a probable memory leak.
10-Sep-2025 14:17:19.493 SEVERE [localhost-startStop-2] org.apache.catalina.loader.WebappClassLoaderBase.checkThreadLocalMapForLeaks The web application [ROOT] created a ThreadLocal with key of type [webwork.action.ActionContext$1] (value [webwork.action.ActionContext$1@4cdae552]) and a value of type [webwork.action.ActionContext] (value [webwork.action.ActionContext@7b100186]) but failed to remove it when the web application was stopped. Threads are going to be renewed over time to try and avoid a probable memory leak.
10-Sep-2025 14:17:19.493 SEVERE [localhost-startStop-2] org.apache.catalina.loader.WebappClassLoaderBase.checkThreadLocalMapForLeaks The web application [ROOT] created a ThreadLocal with key of type [org.picocontainer.DefaultPicoContainer.IntoThreadLocal] (value [org.picocontainer.DefaultPicoContainer$IntoThreadLocal@958e368]) and a value of type [java.lang.Class] (value [class org.picocontainer.ComponentAdapter$NOTHING]) but failed to remove it when the web application was stopped. Threads are going to be renewed over time to try and avoid a probable memory leak.
10-Sep-2025 14:17:19.493 SEVERE [localhost-startStop-2] org.apache.catalina.loader.WebappClassLoaderBase.checkThreadLocalMapForLeaks The web application [ROOT] created a ThreadLocal with key of type [java.lang.ThreadLocal] (value [java.lang.ThreadLocal@7bd7e8b]) and a value of type [com.atlassian.jira.tenancy.JiraTenantImpl] (value [JiraTenantImpl{id='fakeTenantId'}]) but failed to remove it when the web application was stopped. Threads are going to be renewed over time to try and avoid a probable memory leak.
10-Sep-2025 14:17:19.493 SEVERE [localhost-startStop-2] org.apache.catalina.loader.WebappClassLoaderBase.checkThreadLocalMapForLeaks The web application [ROOT] created a ThreadLocal with key of type [webwork.action.ActionContext$1] (value [webwork.action.ActionContext$1@4cdae552]) and a value of type [webwork.action.ActionContext] (value [webwork.action.ActionContext@33571562]) but failed to remove it when the web application was stopped. Threads are going to be renewed over time to try and avoid a probable memory leak.
10-Sep-2025 14:17:19.493 SEVERE [localhost-startStop-2] org.apache.catalina.loader.WebappClassLoaderBase.checkThreadLocalMapForLeaks The web application [ROOT] created a ThreadLocal with key of type [org.picocontainer.DefaultPicoContainer.IntoThreadLocal] (value [org.picocontainer.DefaultPicoContainer$IntoThreadLocal@958e368]) and a value of type [java.lang.Class] (value [class org.picocontainer.ComponentAdapter$NOTHING]) but failed to remove it when the web application was stopped. Threads are going to be renewed over time to try and avoid a probable memory leak.
10-Sep-2025 14:17:19.504 INFO [Thread-3] org.apache.coyote.AbstractProtocol.stop Stopping ProtocolHandler ["http-nio-8080"]
10-Sep-2025 14:17:19.510 INFO [Thread-3] org.apache.coyote.AbstractProtocol.destroy Destroying ProtocolHandler ["http-nio-8080"]
