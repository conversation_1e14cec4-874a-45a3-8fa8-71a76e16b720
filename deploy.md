# JIRA 一键部署指南

## ✅ 部署成功！

你的JIRA环境已经完全配置并成功部署：

### 🎯 **部署状态**
- ✅ **MySQL数据库**：健康运行，端口3307
- ✅ **JIRA应用**：健康运行，端口8080  
- ✅ **MySQL驱动**：已正确挂载到两个位置
- ✅ **日志系统**：已配置并挂载
- ✅ **数据持久化**：使用Docker卷确保数据安全

### 🌐 **访问信息**
- **JIRA地址**：http://localhost:8080
- **数据库配置**（JIRA初始化时使用）：
  - 数据库类型：MySQL 8.0
  - 主机名：`mysql`
  - 端口：`3306`
  - 数据库：`jiradb`
  - 用户名：`root`
  - 密码：`iie.ac.cn`

### 📁 **项目结构**
```
jira/
├── docker-compose.yml       # 主配置文件
├── drivers/                 # MySQL驱动
│   └── mysql-connector-j-8.2.0.jar
├── logs/                    # 日志目录
│   ├── jira-app/           # JIRA应用日志
│   └── mysql/              # MySQL日志
├── backups/                 # 备份目录
└── mysql-config/           # MySQL配置
    └── my.cnf
```

### 🚀 **一键部署命令**
```bash
# 完整部署（包含初始化）
docker-compose up -d

# 查看服务状态
docker-compose ps

# 查看日志
docker-compose logs -f jira
```

### 📊 **服务管理**
```bash
# 停止服务
docker-compose down

# 重启服务
docker-compose restart

# 查看健康状态
docker ps
```

### 🔧 **关键配置特性**

#### 1. **MySQL驱动自动配置**
- 启动时自动复制驱动到正确位置
- 支持JIRA的两个库加载路径
- 无需手动干预

#### 2. **完整的日志管理**
- JIRA应用日志：`./logs/jira-app/`
- MySQL日志：`./logs/mysql/`
- 自动日志轮转和压缩

#### 3. **健康检查**
- JIRA：检查8080端口状态
- MySQL：检查数据库连接
- 120秒启动等待期

#### 4. **数据持久化**
- 使用Docker命名卷
- 数据在容器重建后保持
- 支持备份和恢复

### ⚠️ **重要提示**
1. **首次访问**：打开 http://localhost:8080 进行JIRA初始化
2. **数据库配置**：使用上述数据库连接信息
3. **端口冲突**：确保8080和3307端口未被占用
4. **系统要求**：建议至少4GB可用内存

### 🐛 **故障排除**
1. **驱动问题**：检查 `docker logs jira-driver-setup`
2. **启动失败**：检查 `docker logs jira`
3. **数据库连接**：检查 `docker logs jira-mysql`

---

**状态**：✅ 部署完成，可以开始使用JIRA！
**版本**：JIRA 9.12.4 + MySQL 8.0.35
**部署时间**：`date` 