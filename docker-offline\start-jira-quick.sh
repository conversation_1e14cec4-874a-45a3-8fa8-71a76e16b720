#!/bin/bash

# =======================================================
# JIRA 快速启动脚本
# 在Docker安装完成后快速启动JIRA服务
# =======================================================

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

log_warn() {
    echo -e "${YELLOW}[WARN]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

log_step() {
    echo -e "${BLUE}[STEP]${NC} $1"
}

echo "JIRA 8.13.7 快速启动脚本"
echo "========================================"

# 检查Docker是否安装和运行
check_docker() {
    log_step "检查Docker状态..."
    
    if ! command -v docker >/dev/null 2>&1; then
        log_error "Docker未安装，请先运行: ./install-docker-final.sh"
        exit 1
    fi
    
    if ! systemctl is-active --quiet docker 2>/dev/null && ! pgrep dockerd >/dev/null 2>&1; then
        log_warn "Docker服务未运行，尝试启动..."
        systemctl start docker || {
            log_error "Docker启动失败，请检查安装"
            exit 1
        }
        sleep 3
    fi
    
    if docker info >/dev/null 2>&1; then
        log_info "✅ Docker运行正常"
    else
        log_error "Docker API不可用，请检查Docker状态"
        exit 1
    fi
}

# 检查docker-compose文件
check_compose_file() {
    log_step "检查docker-compose配置..."
    
    cd ..
    
    if [ ! -f "docker-compose.yml" ]; then
        log_error "docker-compose.yml文件不存在"
        log_error "请确保在正确的目录中运行此脚本"
        exit 1
    fi
    
    log_info "✅ docker-compose.yml文件存在"
    
    # 检查MySQL驱动文件
    if [ ! -f "drivers/mysql-connector-java-5.1.49.jar" ]; then
        log_warn "MySQL 5.1驱动文件不存在，尝试下载..."
        
        mkdir -p drivers
        
        local driver_url="https://repo1.maven.org/maven2/mysql/mysql-connector-java/5.1.49/mysql-connector-java-5.1.49.jar"
        
        if command -v wget >/dev/null 2>&1; then
            wget -O "drivers/mysql-connector-java-5.1.49.jar" "$driver_url" || {
                log_error "MySQL驱动下载失败，请手动下载到drivers目录"
                log_error "下载地址: $driver_url"
                exit 1
            }
        elif command -v curl >/dev/null 2>&1; then
            curl -L -o "drivers/mysql-connector-java-5.1.49.jar" "$driver_url" || {
                log_error "MySQL驱动下载失败，请手动下载到drivers目录"
                log_error "下载地址: $driver_url"
                exit 1
            }
        else
            log_error "需要wget或curl来下载MySQL驱动"
            log_error "请手动下载: $driver_url"
            log_error "保存到: drivers/mysql-connector-java-5.1.49.jar"
            exit 1
        fi
        
        log_info "✅ MySQL驱动下载完成"
    else
        log_info "✅ MySQL驱动文件存在"
    fi
}

# 创建必要的目录
create_directories() {
    log_step "创建必要的目录..."
    
    local directories=(
        "data/jira-data"
        "data/mysql-data"
        "logs/jira-logs"
        "logs/mysql-logs"
        "backups"
    )
    
    for dir in "${directories[@]}"; do
        if [ ! -d "$dir" ]; then
            mkdir -p "$dir"
            log_info "✅ 创建目录: $dir"
        fi
    done
    
    # 设置权限
    chmod -R 755 data/ logs/ 2>/dev/null || true
}

# 启动服务
start_services() {
    log_step "启动JIRA服务..."
    
    # 停止现有服务
    log_info "停止现有服务..."
    docker compose down 2>/dev/null || true
    
    # 启动服务
    log_info "启动新服务..."
    if docker compose up -d; then
        log_info "✅ 服务启动成功"
    else
        log_error "服务启动失败"
        log_error "请检查docker-compose.yml配置"
        exit 1
    fi
}

# 等待服务就绪
wait_for_services() {
    log_step "等待服务启动..."
    
    # 等待MySQL启动
    log_info "等待MySQL启动..."
    local mysql_wait=0
    local max_mysql_wait=60
    
    while [ $mysql_wait -lt $max_mysql_wait ]; do
        if docker compose exec -T mysql mysqladmin ping -h localhost --silent 2>/dev/null; then
            log_info "✅ MySQL已就绪"
            break
        fi
        
        sleep 2
        mysql_wait=$((mysql_wait + 2))
        
        if [ $((mysql_wait % 10)) -eq 0 ]; then
            log_info "等待MySQL启动... ($mysql_wait/$max_mysql_wait秒)"
        fi
    done
    
    if [ $mysql_wait -ge $max_mysql_wait ]; then
        log_error "MySQL启动超时"
        log_error "请检查MySQL容器日志: docker compose logs mysql"
        exit 1
    fi
    
    # 等待JIRA启动（这需要更长时间）
    log_info "等待JIRA启动（这可能需要3-5分钟）..."
    log_info "您可以通过以下命令查看启动进度:"
    log_info "docker compose logs -f jira"
}

# 显示访问信息
show_access_info() {
    log_step "显示访问信息..."
    
    echo ""
    echo "========================================"
    echo "  JIRA 8.13.7 启动完成"
    echo "========================================"
    echo ""
    echo "🌐 访问信息:"
    echo "   JIRA地址: http://localhost:8080"
    echo "   MySQL地址: localhost:3307"
    echo ""
    echo "🗄️ 数据库配置:"
    echo "   数据库类型: MySQL 5.7"
    echo "   主机名: mysql"
    echo "   端口: 3306"
    echo "   数据库名: jiradb"
    echo "   用户名: jirauser"
    echo "   密码: jira_password_2021"
    echo ""
    echo "📋 常用命令:"
    echo "   查看服务状态: docker compose ps"
    echo "   查看JIRA日志: docker compose logs -f jira"
    echo "   查看MySQL日志: docker compose logs -f mysql"
    echo "   停止服务: docker compose down"
    echo "   重启服务: docker compose restart"
    echo ""
    echo "⚠️ 重要提示:"
    echo "   1. JIRA首次启动需要3-5分钟，请耐心等待"
    echo "   2. 访问 http://localhost:8080 开始JIRA配置"
    echo "   3. 选择数据库配置时使用上述MySQL信息"
    echo "   4. 如果遇到问题，请查看容器日志"
    echo ""
}

# 主函数
main() {
    check_docker
    check_compose_file
    create_directories
    start_services
    wait_for_services
    show_access_info
}

# 运行主函数
main "$@"
