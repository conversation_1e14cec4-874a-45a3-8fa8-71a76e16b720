#!/bin/bash

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

log_warn() {
    echo -e "${YELLOW}[WARN]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

log_step() {
    echo -e "${BLUE}[STEP]${NC} $1"
}

# 检查Docker是否安装
check_docker() {
    if ! command -v docker &> /dev/null; then
        log_error "Docker未安装，请先安装Docker"
        log_info "运行: ./docker-offline/install-docker-centos7.sh"
        exit 1
    fi
    
    if ! docker info &> /dev/null; then
        log_error "Docker服务未运行"
        log_info "启动Docker: sudo systemctl start docker"
        exit 1
    fi
    
    log_info "Docker检查通过: $(docker --version)"
}

# 检查Docker Compose
check_compose() {
    if ! docker compose version &> /dev/null; then
        log_error "Docker Compose未安装"
        exit 1
    fi
    
    log_info "Docker Compose检查通过: $(docker compose version)"
}

# 检查必要文件
check_files() {
    local required_files=(
        "docker-compose.yml"
        "drivers/mysql-connector-j-8.2.0.jar"
        "mysql-config/my.cnf"
    )
    
    log_step "检查必要文件..."
    for file in "${required_files[@]}"; do
        if [ ! -f "$file" ]; then
            log_error "缺少必要文件: $file"
            exit 1
        fi
        log_info "✓ $file"
    done
}

# 创建目录结构
create_directories() {
    log_step "创建目录结构..."
    
    local directories=(
        "data/jira-data"
        "data/mysql-data"
        "logs/jira-logs"
        "logs/mysql-logs"
        "backups"
    )
    
    for dir in "${directories[@]}"; do
        mkdir -p "$dir"
        log_info "✓ $dir"
    done
}

# 设置目录权限
set_permissions() {
    log_step "设置目录权限..."
    
    # JIRA数据目录权限 (UID 2001)
    sudo chown -R 2001:2001 data/jira-data/
    sudo chmod -R 755 data/jira-data/
    
    # MySQL数据目录权限 (UID 999)
    sudo chown -R 999:999 data/mysql-data/
    sudo chmod -R 755 data/mysql-data/
    
    # 日志目录权限
    sudo chmod -R 755 logs/
    
    # 备份目录权限
    sudo chmod -R 755 backups/
    
    # MySQL驱动权限
    sudo chmod 644 drivers/mysql-connector-j-8.2.0.jar
    
    # MySQL配置文件权限
    sudo chmod 644 mysql-config/my.cnf
    
    log_info "目录权限设置完成"
}

# 检查系统资源
check_resources() {
    log_step "检查系统资源..."
    
    # 检查内存
    local total_mem=$(free -m | awk 'NR==2{printf "%.1f", $2/1024}')
    if (( $(echo "$total_mem < 6.0" | bc -l) )); then
        log_warn "系统内存不足6GB，建议增加内存以获得更好性能"
        log_warn "当前内存: ${total_mem}GB"
    else
        log_info "内存检查通过: ${total_mem}GB"
    fi
    
    # 检查磁盘空间
    local available_space=$(df -BG . | awk 'NR==2{print $4}' | sed 's/G//')
    if (( available_space < 20 )); then
        log_warn "磁盘空间不足20GB，建议释放更多空间"
        log_warn "可用空间: ${available_space}GB"
    else
        log_info "磁盘空间检查通过: ${available_space}GB可用"
    fi
}

# 检查网络端口
check_ports() {
    log_step "检查网络端口..."
    
    local ports=(8080 3307)
    for port in "${ports[@]}"; do
        if netstat -tuln | grep -q ":$port "; then
            log_error "端口 $port 已被占用"
            log_info "检查占用进程: sudo netstat -tulpn | grep :$port"
            exit 1
        else
            log_info "✓ 端口 $port 可用"
        fi
    done
}

# 预下载镜像
pull_images() {
    log_step "预下载Docker镜像..."
    
    docker pull atlassian/jira-software:latest
    docker pull mysql:8.0.35
    
    log_info "镜像下载完成"
}

# 启动服务
start_services() {
    log_step "启动JIRA服务..."
    
    # 启动服务
    docker compose up -d
    
    log_info "服务启动完成"
}

# 等待服务就绪
wait_for_services() {
    log_step "等待服务就绪..."
    
    # 等待MySQL就绪
    log_info "等待MySQL服务启动..."
    local mysql_ready=false
    for i in {1..60}; do
        if docker exec jira-mysql mysqladmin ping -h localhost -u root -p"iie.ac.cn" 2>/dev/null; then
            mysql_ready=true
            break
        fi
        echo -n "."
        sleep 5
    done
    echo
    
    if [ "$mysql_ready" = false ]; then
        log_error "MySQL服务启动超时"
        docker compose logs jira-mysql
        exit 1
    fi
    log_info "✓ MySQL服务就绪"
    
    # 等待JIRA就绪
    log_info "等待JIRA服务启动... (这可能需要几分钟)"
    local jira_ready=false
    for i in {1..120}; do
        if curl -f http://localhost:8080/status 2>/dev/null; then
            jira_ready=true
            break
        fi
        echo -n "."
        sleep 10
    done
    echo
    
    if [ "$jira_ready" = false ]; then
        log_warn "JIRA服务启动可能需要更多时间"
        log_info "请稍后访问 http://$(hostname -I | awk '{print $1}'):8080 检查状态"
    else
        log_info "✓ JIRA服务就绪"
    fi
}

# 验证部署
verify_deployment() {
    log_step "验证部署..."
    
    # 检查容器状态
    if ! docker compose ps | grep -q "Up"; then
        log_error "某些容器未正常运行"
        docker compose ps
        exit 1
    fi
    
    # 检查MySQL驱动
    if docker exec jira-server ls -la /opt/atlassian/jira/lib/ | grep -q mysql; then
        log_info "✓ MySQL驱动加载正常"
    else
        log_warn "MySQL驱动可能未正确加载"
    fi
    
    # 显示服务状态
    docker compose ps
    
    log_info "部署验证完成"
}

# 显示访问信息
show_access_info() {
    local server_ip=$(hostname -I | awk '{print $1}')
    
    echo
    log_info "=========================================="
    log_info "JIRA 部署完成！"
    log_info "=========================================="
    echo
    log_info "访问信息："
    log_info "- JIRA Web界面: http://${server_ip}:8080"
    log_info "- MySQL端口: ${server_ip}:3307"
    echo
    log_info "初始配置："
    log_info "- 数据库类型: MySQL"
    log_info "- 数据库主机: jira-mysql"
    log_info "- 数据库端口: 3306"
    log_info "- 数据库名: jiradb"
    log_info "- 用户名: root"
    log_info "- 密码: iie.ac.cn"
    echo
    log_info "管理命令："
    log_info "- 查看状态: docker compose ps"
    log_info "- 查看日志: docker compose logs -f"
    log_info "- 停止服务: docker compose down"
    log_info "- 重启服务: docker compose restart"
    echo
    log_info "数据路径："
    log_info "- JIRA数据: ./data/jira-data/"
    log_info "- MySQL数据: ./data/mysql-data/"
    log_info "- 日志文件: ./logs/"
    log_info "- 备份目录: ./backups/"
    echo
}

# 创建管理脚本
create_management_scripts() {
    log_step "创建管理脚本..."
    
    # 备份脚本
    cat > backup.sh << 'EOF'
#!/bin/bash
# JIRA备份脚本

DATE=$(date +%Y%m%d_%H%M%S)
BACKUP_DIR="./backups"

echo "开始备份 JIRA..."

# 备份MySQL数据库
docker exec jira-mysql mysqldump -u root -p"iie.ac.cn" jiradb > "$BACKUP_DIR/jira-mysql-$DATE.sql"

# 备份JIRA数据目录
tar -czf "$BACKUP_DIR/jira-data-$DATE.tar.gz" -C data jira-data/

echo "备份完成: $BACKUP_DIR/"
ls -lh "$BACKUP_DIR/"*$DATE*
EOF

    # 恢复脚本
    cat > restore.sh << 'EOF'
#!/bin/bash
# JIRA恢复脚本

if [ $# -ne 1 ]; then
    echo "用法: $0 <日期标识>"
    echo "例如: $0 20240109_143000"
    exit 1
fi

DATE=$1
BACKUP_DIR="./backups"

echo "开始恢复 JIRA ($DATE)..."

# 停止服务
docker compose down

# 恢复MySQL数据库
if [ -f "$BACKUP_DIR/jira-mysql-$DATE.sql" ]; then
    docker compose up -d jira-mysql
    sleep 30
    docker exec -i jira-mysql mysql -u root -p"iie.ac.cn" jiradb < "$BACKUP_DIR/jira-mysql-$DATE.sql"
    echo "MySQL数据库恢复完成"
else
    echo "MySQL备份文件不存在: $BACKUP_DIR/jira-mysql-$DATE.sql"
fi

# 恢复JIRA数据目录
if [ -f "$BACKUP_DIR/jira-data-$DATE.tar.gz" ]; then
    rm -rf data/jira-data/*
    tar -xzf "$BACKUP_DIR/jira-data-$DATE.tar.gz" -C data/
    sudo chown -R 2001:2001 data/jira-data/
    echo "JIRA数据恢复完成"
else
    echo "JIRA数据备份文件不存在: $BACKUP_DIR/jira-data-$DATE.tar.gz"
fi

# 启动所有服务
docker compose up -d

echo "恢复完成"
EOF

    chmod +x backup.sh restore.sh
    
    log_info "管理脚本创建完成: backup.sh, restore.sh"
}

# 主函数
main() {
    echo "========================================"
    echo "JIRA Docker 生产环境部署"
    echo "========================================"
    echo
    
    check_docker
    check_compose
    check_files
    check_resources
    check_ports
    
    read -p "确认开始部署JIRA? (y/N): " -n 1 -r
    echo
    if [[ ! $REPLY =~ ^[Yy]$ ]]; then
        log_info "部署已取消"
        exit 0
    fi
    
    create_directories
    set_permissions
    pull_images
    start_services
    wait_for_services
    verify_deployment
    create_management_scripts
    show_access_info
    
    log_info "JIRA部署完成！"
}

# 错误处理
trap 'log_error "部署过程中发生错误，请检查日志"; docker compose logs' ERR

# 执行主函数
main "$@" 