services:
  jira:
    image: atlassian/jira-software:8.8.0
    container_name: jira-server
    restart: unless-stopped
    depends_on:
      mysql:
        condition: service_healthy
    ports:
      - "8080:8080"
    environment:
      # 数据库连接配置  
      - ATL_JDBC_URL=**************************************************************************************************************************************************************
      - ATL_JDBC_USER=root
      - ATL_JDBC_PASSWORD=iie.ac.cn
      - ATL_DB_DRIVER=com.mysql.cj.jdbc.Driver
      - ATL_DB_TYPE=mysql57
      
      # JVM 内存配置
      - JVM_MINIMUM_MEMORY=1024m
      - JVM_MAXIMUM_MEMORY=2048m
      
      # 基本配置
      - SET_PERMISSIONS=true
      - TZ=Asia/Shanghai
    volumes:
      # JIRA 数据目录
      - ./data/jira-data:/var/atlassian/application-data/jira
      # JIRA 日志目录挂载
      - ./logs/jira-logs:/opt/atlassian/jira/logs
      # MySQL 5.1 驱动挂载（适配MySQL 5.7）
      - ./drivers/mysql-connector-java-5.1.49.jar:/opt/atlassian/jira/lib/mysql-connector-java-5.1.49.jar:ro
      # 备份目录挂载
      - ./backups:/var/atlassian/backups
    networks:
      - jira-network

  mysql:
    image: mysql:5.7
    container_name: mysql
    restart: unless-stopped
    ports:
      - "3307:3306"
    environment:
      - MYSQL_ROOT_PASSWORD=iie.ac.cn
      - MYSQL_DATABASE=jiradb
      - TZ=Asia/Shanghai
    volumes:
      # MySQL 数据持久化
      - ./data/mysql-data:/var/lib/mysql
      # MySQL 日志目录挂载
      - ./logs/mysql-logs:/var/log/mysql
      # MySQL 配置文件（设置适当权限）
      - ./mysql-config/my.cnf:/etc/mysql/conf.d/my.cnf:ro
    healthcheck:
      test: ["CMD-SHELL", "mysqladmin ping -h 127.0.0.1 -uroot -p$MYSQL_ROOT_PASSWORD || exit 1"]
      interval: 30s
      timeout: 10s
      retries: 5
      start_period: 60s
    networks:
      - jira-network

networks:
  jira-network:
    driver: bridge
