2025-09-10 13:19:33,641+0800 localhost-startStop-1 INFO      [c.a.jira.startup.JiraHomeStartupCheck] The jira.home directory '/var/atlassian/application-data/jira' is validated and locked for exclusive use by this instance.
2025-09-10 13:19:33,718+0800 JIRA-Bootstrap INFO      [c.a.jira.startup.JiraStartupLogger] 
    
    ****************
    Jira starting...
    ****************
    
2025-09-10 13:19:33,808+0800 JIRA-Bootstrap INFO      [c.a.jira.startup.JiraStartupLogger] 
    
    ___ Environment _____________________________
    
         JIRA Build                                    : 8.8.0#808000-sha1:e2c7e59ae165efc6ad6b529150e24d091b9947bf
         Build Date                                    : Thu Mar 19 00:00:00 CST 2020
         JIRA Installation Type                        : Standalone
         Application Server                            : Apache Tomcat/8.5.42 - Servlet API 3.1
         Java Version                                  : 11.0.15 - Eclipse Adoptium
         Current Working Directory                     : /var/atlassian/application-data/jira
         Maximum Allowable Memory                      : 2048MB
         Total Memory                                  : 1024MB
         Free Memory                                   : 752MB
         Used Memory                                   : 272MB
         Memory Pool: CodeHeap 'non-nmethods'          : CodeHeap 'non-nmethods': init = 7598080(7420K) used = 2981760(2911K) committed = 7598080(7420K) max = 7598080(7420K)
         Memory Pool: Metaspace                        : Metaspace: init = 0(0K) used = 27564232(26918K) committed = 28573696(27904K) max = -1(-1K)
         Memory Pool: CodeHeap 'profiled nmethods'     : CodeHeap 'profiled nmethods': init = 33554432(32768K) used = 9287936(9070K) committed = 33554432(32768K) max = 264634368(258432K)
         Memory Pool: Compressed Class Space           : Compressed Class Space: init = 0(0K) used = 2824280(2758K) committed = 3145728(3072K) max = 1073741824(1048576K)
         Memory Pool: G1 Eden Space                    : G1 Eden Space: init = 56623104(55296K) used = 260046848(253952K) committed = 355467264(347136K) max = -1(-1K)
         Memory Pool: G1 Old Gen                       : G1 Old Gen: init = 1017118720(993280K) used = 16943984(16546K) committed = 709885952(693248K) max = 2147483648(2097152K)
         Memory Pool: G1 Survivor Space                : G1 Survivor Space: init = 0(0K) used = 8388608(8192K) committed = 8388608(8192K) max = -1(-1K)
         Memory Pool: CodeHeap 'non-profiled nmethods' : CodeHeap 'non-profiled nmethods': init = 33554432(32768K) used = 3094528(3022K) committed = 33554432(32768K) max = 264638464(258436K)
         JVM Input Arguments                           : --add-opens=java.base/java.lang=ALL-UNNAMED --add-opens=java.base/java.io=ALL-UNNAMED --add-opens=java.rmi/sun.rmi.transport=ALL-UNNAMED -Djava.util.logging.config.file=/opt/atlassian/jira/conf/logging.properties -Djava.util.logging.manager=org.apache.juli.ClassLoaderLogManager -Xms1024m -Xmx2048m -XX:InitialCodeCacheSize=32m -XX:ReservedCodeCacheSize=512m -Djava.awt.headless=true -Datlassian.standalone=JIRA -Dorg.apache.jasper.runtime.BodyContentImpl.LIMIT_BUFFER=true -Dmail.mime.decodeparameters=true -Dorg.dom4j.factory=com.atlassian.core.xml.InterningDocumentFactory -XX:-OmitStackTraceInFastThrow -Djava.locale.providers=COMPAT -Djira.home=/var/atlassian/application-data/jira -Datlassian.plugins.startup.options=-fg -Djdk.tls.ephemeralDHKeySize=2048 -Djava.protocol.handler.pkgs=org.apache.catalina.webresources -Dorg.apache.catalina.security.SecurityListener.UMASK=0027 -Xlog:gc*:file=/opt/atlassian/jira/logs/atlassian-jira-gc-%t.log:time,uptime:filecount=5,filesize=20M -XX:+ExplicitGCInvokesConcurrent -Dignore.endorsed.dirs= -Dcatalina.base=/opt/atlassian/jira -Dcatalina.home=/opt/atlassian/jira -Djava.io.tmpdir=/opt/atlassian/jira/temp
         Java Compatibility Information                : JIRA version = 8.8.0, Java Version = 11.0.15
    
    ___ Java System Properties _________________
    
         atlassian.plugins.startup.options             : -fg
         atlassian.standalone                          : JIRA
         awt.toolkit                                   : sun.awt.X11.XToolkit
         catalina.base                                 : /opt/atlassian/jira
         catalina.home                                 : /opt/atlassian/jira
         catalina.useNaming                            : true
         common.loader                                 : "${catalina.base}/lib",
                                                         "${catalina.base}/lib/*.jar",
                                                         "${catalina.home}/lib",
                                                         "${catalina.home}/lib/*.jar"
         file.encoding                                 : UTF-8
         ignore.endorsed.dirs                          : 
         java.awt.graphicsenv                          : sun.awt.X11GraphicsEnvironment
         java.awt.headless                             : true
         java.awt.printerjob                           : sun.print.PSPrinterJob
         java.class.version                            : 55.0
         java.home                                     : /opt/java/openjdk
         java.io.tmpdir                                : /opt/atlassian/jira/temp
         java.locale.providers                         : COMPAT
         java.naming.factory.initial                   : org.apache.naming.java.javaURLContextFactory
         java.naming.factory.url.pkgs                  : org.apache.naming
         java.protocol.handler.pkgs                    : org.apache.catalina.webresources
         java.runtime.name                             : OpenJDK Runtime Environment
         java.runtime.version                          : 11.0.15+10
         java.specification.name                       : Java Platform API Specification
         java.specification.vendor                     : Oracle Corporation
         java.specification.version                    : 11
         java.util.logging.config.file                 : /opt/atlassian/jira/conf/logging.properties
         java.util.logging.manager                     : org.apache.juli.ClassLoaderLogManager
         java.vendor                                   : Eclipse Adoptium
         java.vendor.url                               : https://adoptium.net/
         java.vendor.url.bug                           : https://github.com/adoptium/adoptium-support/issues
         java.vendor.version                           : Temurin-11.0.15+10
         java.version                                  : 11.0.15
         java.version.date                             : 2022-04-19
         java.vm.compressedOopsMode                    : 32-bit
         java.vm.info                                  : mixed mode
         java.vm.name                                  : OpenJDK 64-Bit Server VM
         java.vm.specification.name                    : Java Virtual Machine Specification
         java.vm.specification.vendor                  : Oracle Corporation
         java.vm.specification.version                 : 11
         java.vm.vendor                                : Eclipse Adoptium
         java.vm.version                               : 11.0.15+10
         jdk.debug                                     : release
         jdk.tls.ephemeralDHKeySize                    : 2048
         jira.home                                     : /var/atlassian/application-data/jira
         mail.mime.decodeparameters                    : true
         org.apache.catalina.security.SecurityListener.UMASK : 0027
         org.apache.jasper.runtime.BodyContentImpl.LIMIT_BUFFER : true
         org.dom4j.factory                             : com.atlassian.core.xml.InterningDocumentFactory
         os.arch                                       : amd64
         os.name                                       : Linux
         os.version                                    : ********-microsoft-standard-WSL2
         package.access                                : sun.,
                                                         org.apache.catalina.,
                                                         org.apache.coyote.,
                                                         org.apache.jasper.,
                                                         org.apache.tomcat.
         package.definition                            : sun.,
                                                         java.,
                                                         org.apache.catalina.,
                                                         org.apache.coyote.,
                                                         org.apache.jasper.,
                                                         org.apache.naming.,
                                                         org.apache.tomcat.
         server.loader                                 : 
         shared.loader                                 : 
         sun.arch.data.model                           : 64
         sun.boot.library.path                         : /opt/java/openjdk/lib
         sun.cpu.endian                                : little
         sun.cpu.isalist                               : 
         sun.io.unicode.encoding                       : UnicodeLittle
         sun.java.command                              : org.apache.catalina.startup.Bootstrap start
         sun.java.launcher                             : SUN_STANDARD
         sun.jnu.encoding                              : UTF-8
         sun.management.compiler                       : HotSpot 64-Bit Tiered Compilers
         sun.os.patch.level                            : unknown
         tomcat.util.buf.StringCache.byte.enabled      : true
         tomcat.util.scan.StandardJarScanFilter.jarsToScan : log4j-taglib*.jar,
                                                         log4j-web*.jar,
                                                         log4javascript*.jar,
                                                         slf4j-taglib*.jar
         tomcat.util.scan.StandardJarScanFilter.jarsToSkip : annotations-api.jar,
                                                         ant-junit*.jar,
                                                         ant-launcher.jar,
                                                         ant.jar,
                                                         asm-*.jar,
                                                         aspectj*.jar,
                                                         bootstrap.jar,
                                                         catalina-ant.jar,
                                                         catalina-ha.jar,
                                                         catalina-jmx-remote.jar,
                                                         catalina-storeconfig.jar,
                                                         catalina-tribes.jar,
                                                         catalina-ws.jar,
                                                         catalina.jar,
                                                         cglib-*.jar,
                                                         cobertura-*.jar,
                                                         commons-beanutils*.jar,
                                                         commons-codec*.jar,
                                                         commons-collections*.jar,
                                                         commons-daemon.jar,
                                                         commons-dbcp*.jar,
                                                         commons-digester*.jar,
                                                         commons-fileupload*.jar,
                                                         commons-httpclient*.jar,
                                                         commons-io*.jar,
                                                         commons-lang*.jar,
                                                         commons-logging*.jar,
                                                         commons-math*.jar,
                                                         commons-pool*.jar,
                                                         dom4j-*.jar,
                                                         easymock-*.jar,
                                                         ecj-*.jar,
                                                         el-api.jar,
                                                         geronimo-spec-jaxrpc*.jar,
                                                         h2*.jar,
                                                         hamcrest-*.jar,
                                                         hibernate*.jar,
                                                         httpclient*.jar,
                                                         icu4j-*.jar,
                                                         jasper-el.jar,
                                                         jasper.jar,
                                                         jaspic-api.jar,
                                                         jaxb-*.jar,
                                                         jaxen-*.jar,
                                                         jdom-*.jar,
                                                         jetty-*.jar,
                                                         jmx-tools.jar,
                                                         jmx.jar,
                                                         jsp-api.jar,
                                                         jstl.jar,
                                                         jta*.jar,
                                                         junit-*.jar,
                                                         junit.jar,
                                                         log4j*.jar,
                                                         mail*.jar,
                                                         objenesis-*.jar,
                                                         oraclepki.jar,
                                                         oro-*.jar,
                                                         servlet-api-*.jar,
                                                         servlet-api.jar,
                                                         slf4j*.jar,
                                                         taglibs-standard-spec-*.jar,
                                                         tagsoup-*.jar,
                                                         tomcat-api.jar,
                                                         tomcat-coyote.jar,
                                                         tomcat-dbcp.jar,
                                                         tomcat-i18n-*.jar,
                                                         tomcat-jdbc.jar,
                                                         tomcat-jni.jar,
                                                         tomcat-juli-adapters.jar,
                                                         tomcat-juli.jar,
                                                         tomcat-util-scan.jar,
                                                         tomcat-util.jar,
                                                         tomcat-websocket.jar,
                                                         tools.jar,
                                                         websocket-api.jar,
                                                         wsdl4j*.jar,
                                                         xercesImpl.jar,
                                                         xml-apis.jar,
                                                         xmlParserAPIs-*.jar,
                                                         xmlParserAPIs.jar,
                                                         xom-*.jar
         user.country                                  : US
         user.dir                                      : /var/atlassian/application-data/jira
         user.home                                     : /var/atlassian/application-data/jira
         user.language                                 : en
         user.name                                     : jira
         user.timezone                                 : Asia/Shanghai
    
2025-09-10 13:19:33,982+0800 JIRA-Bootstrap INFO      [c.a.j.config.database.SystemDatabaseConfigurationLoader] Reading database configuration from /var/atlassian/application-data/jira/dbconfig.xml
2025-09-10 13:19:34,068+0800 JIRA-Bootstrap INFO      [c.a.j.config.database.SystemDatabaseConfigurationLoader] Reading database configuration from /var/atlassian/application-data/jira/dbconfig.xml
2025-09-10 13:19:34,072+0800 JIRA-Bootstrap INFO      [c.a.j.config.database.SystemDatabaseConfigurationLoader] Reading database configuration from /var/atlassian/application-data/jira/dbconfig.xml
2025-09-10 13:19:34,319+0800 JIRA-Bootstrap INFO      [c.a.j.c.cache.pauser.NonClusteredReplicationPauserManager] Non-clustered mode: ReplicationPauserManager implemented by NonClusteredReplicationPauserManager 
2025-09-10 13:19:34,321+0800 JIRA-Bootstrap INFO      [c.a.jira.plugin.JiraCacheResetter] [jira-cache-reseter] Created and registered for events
2025-09-10 13:19:34,323+0800 JIRA-Bootstrap INFO      [c.a.jira.plugin.PluginTransactionListener] [plugin-transaction] Created, registered for events and schedulled stats job
2025-09-10 13:19:34,335+0800 JIRA-Bootstrap INFO      [c.a.jira.i18n.CachingI18nFactory] [i18n-caching-factory] Created
2025-09-10 13:19:34,778+0800 JIRA-Bootstrap INFO      [c.a.j.config.database.SystemDatabaseConfigurationLoader] Reading database configuration from /var/atlassian/application-data/jira/dbconfig.xml
2025-09-10 13:19:34,790+0800 JIRA-Bootstrap INFO      [c.a.jira.startup.JiraStartupLogger] 
    
    ___ Starting the JIRA Plugin System _________________
    
2025-09-10 13:19:34,792+0800 JIRA-Bootstrap INFO      [c.a.plugin.manager.DefaultPluginManager] Plugin system earlyStartup begun
2025-09-10 13:19:40,483+0800 FelixStartLevel WARN      [o.e.g.b.e.internal.support.ExtenderConfiguration] Gemini Blueprint extensions bundle not present, annotation processing disabled.
2025-09-10 13:19:42,516+0800 JIRA-Bootstrap INFO      [c.a.plugin.loaders.ScanningPluginLoader] No plugins found to be deployed
2025-09-10 13:19:44,250+0800 ThreadPoolAsyncTaskExecutor::Thread 2 WARN      [c.a.p.s.scanner.util.ProductFilterUtil] Couldn't detect product, will use ProductFilter.ALL
2025-09-10 13:19:44,437+0800 JIRA-Bootstrap INFO      [c.a.plugin.util.WaitUntil] Plugins that have yet to be enabled: (1): [com.atlassian.soy.soy-template-plugin], 300 seconds remaining
2025-09-10 13:19:45,438+0800 JIRA-Bootstrap INFO      [c.a.plugin.util.WaitUntil] Plugins that have yet to be enabled: (1): [com.atlassian.soy.soy-template-plugin], 298 seconds remaining
2025-09-10 13:19:46,439+0800 JIRA-Bootstrap INFO      [c.a.plugin.util.WaitUntil] Plugins that have yet to be enabled: (1): [com.atlassian.soy.soy-template-plugin], 297 seconds remaining
2025-09-10 13:19:47,639+0800 JIRA-Bootstrap INFO      [c.a.plugin.manager.DefaultPluginManager] Plugin system earlyStartup ended
2025-09-10 13:19:47,647+0800 JIRA-Bootstrap INFO      [c.a.jira.i18n.CachingI18nFactory] [i18n-caching-factory] Starting
2025-09-10 13:19:47,708+0800 JIRA-Bootstrap INFO      [c.a.j.config.database.SystemDatabaseConfigurationLoader] Reading database configuration from /var/atlassian/application-data/jira/dbconfig.xml
2025-09-10 13:19:47,726+0800 JIRA-Bootstrap INFO      [c.a.j.instrumentation.external.DatabaseExternalGauges] Installing DBCP monitoring instruments: DatabaseExternalGauges.JiraDbcpInstruments[instruments=[DBCP_MAX, DBCP_ACTIVE, DBCP_IDLE],objectName=com.atlassian.jira:name=BasicDataSource]
2025-09-10 13:19:47,745+0800 JIRA-Bootstrap INFO      [c.a.jira.startup.JiraStartupLogger] Running Jira startup checks.
2025-09-10 13:19:47,745+0800 JIRA-Bootstrap INFO      [c.a.jira.startup.JiraStartupLogger] Jira pre-database startup checks completed successfully.
2025-09-10 13:19:47,779+0800 JIRA-Bootstrap INFO      [c.a.j.config.database.DatabaseConfigurationManagerImpl] The database is not yet configured. Enqueuing Database Checklist Launcher on post-database-configured-but-pre-database-activated queue
2025-09-10 13:19:47,781+0800 JIRA-Bootstrap INFO      [c.a.j.config.database.DatabaseConfigurationManagerImpl] The database is not yet configured. Enqueuing Post database-configuration launchers on post-database-activated queue
2025-09-10 13:19:47,785+0800 JIRA-Bootstrap INFO      [c.a.jira.startup.LauncherContextListener] Memory Usage:
    ---------------------------------------------------------------------------------
      Heap memory     :  Used:  251 MiB.  Committed: 1024 MiB.  Max: 2048 MiB
      Non-heap memory :  Used:   93 MiB.  Committed:  136 MiB.  Max: 1536 MiB
    ---------------------------------------------------------------------------------
      TOTAL           :  Used:  344 MiB.  Committed: 1160 MiB.  Max: 3584 MiB
    ---------------------------------------------------------------------------------
2025-09-10 13:20:38,211+0800 http-nio-8080-exec-2 WARN anonymous 800x2x1 - ********** /secure/SetupMode!default.jspa [webresource] cyclic plugin resource dependency has been detected with: com.atlassian.plugins.atlassian-plugins-webresource-plugin:context-path, stack trace: [jira.webresources:jira-setup, jira.webresources:_polyfills, com.atlassian.frontend.atlassian-frontend-runtime-plugin:core-js, com.atlassian.plugins.atlassian-plugins-webresource-plugin:context-path, com.atlassian.auiplugin:aui-core, com.atlassian.auiplugin:common-runtime]
2025-09-10 13:20:38,211+0800 http-nio-8080-exec-2 WARN anonymous 800x2x1 - ********** /secure/SetupMode!default.jspa [webresource] cyclic plugin resource dependency has been detected with: com.atlassian.plugins.atlassian-plugins-webresource-plugin:context-path, stack trace: [jira.webresources:jira-setup, jira.webresources:_polyfills, com.atlassian.frontend.atlassian-frontend-runtime-plugin:core-js, com.atlassian.plugins.atlassian-plugins-webresource-plugin:context-path, com.atlassian.auiplugin:aui-core]
2025-09-10 13:20:38,212+0800 http-nio-8080-exec-2 WARN anonymous 800x2x1 - ********** /secure/SetupMode!default.jspa [webresource] cyclic plugin resource dependency has been detected with: com.atlassian.plugins.atlassian-plugins-webresource-plugin:context-path, stack trace: [jira.webresources:jira-setup, jira.webresources:_polyfills, com.atlassian.frontend.atlassian-frontend-runtime-plugin:core-js, com.atlassian.plugins.atlassian-plugins-webresource-plugin:context-path, com.atlassian.auiplugin:aui-core, com.atlassian.auiplugin:split_aui.splitchunk.0d131bcbf1]
2025-09-10 13:20:38,212+0800 http-nio-8080-exec-2 WARN anonymous 800x2x1 - ********** /secure/SetupMode!default.jspa [webresource] cyclic plugin resource dependency has been detected with: com.atlassian.plugins.atlassian-plugins-webresource-plugin:context-path, stack trace: [jira.webresources:jira-setup, jira.webresources:_polyfills, com.atlassian.frontend.atlassian-frontend-runtime-plugin:core-js, com.atlassian.plugins.atlassian-plugins-webresource-plugin:context-path, com.atlassian.auiplugin:aui-core, com.atlassian.auiplugin:split_aui.splitchunk.444efc83be]
2025-09-10 13:20:38,212+0800 http-nio-8080-exec-2 WARN anonymous 800x2x1 - ********** /secure/SetupMode!default.jspa [webresource] cyclic plugin resource dependency has been detected with: com.atlassian.plugins.atlassian-plugins-webresource-plugin:context-path, stack trace: [jira.webresources:jira-setup, jira.webresources:_polyfills, com.atlassian.frontend.atlassian-frontend-runtime-plugin:core-js, com.atlassian.plugins.atlassian-plugins-webresource-plugin:context-path, com.atlassian.auiplugin:aui-core, com.atlassian.auiplugin:split_aui.splitchunk.78b1c5d416]
2025-09-10 13:20:38,212+0800 http-nio-8080-exec-2 WARN anonymous 800x2x1 - ********** /secure/SetupMode!default.jspa [webresource] cyclic plugin resource dependency has been detected with: com.atlassian.plugins.atlassian-plugins-webresource-plugin:context-path, stack trace: [jira.webresources:jira-setup, jira.webresources:_polyfills, com.atlassian.frontend.atlassian-frontend-runtime-plugin:core-js, com.atlassian.plugins.atlassian-plugins-webresource-plugin:context-path, com.atlassian.auiplugin:aui-core, com.atlassian.auiplugin:split_aui.splitchunk.9bbf750f52]
2025-09-10 13:20:38,212+0800 http-nio-8080-exec-2 WARN anonymous 800x2x1 - ********** /secure/SetupMode!default.jspa [webresource] cyclic plugin resource dependency has been detected with: com.atlassian.plugins.atlassian-plugins-webresource-plugin:context-path, stack trace: [jira.webresources:jira-setup, jira.webresources:_polyfills, com.atlassian.frontend.atlassian-frontend-runtime-plugin:core-js, com.atlassian.plugins.atlassian-plugins-webresource-plugin:context-path, com.atlassian.auiplugin:aui-core, com.atlassian.auiplugin:split_aui.splitchunk.479fe6ee76]
2025-09-10 13:20:38,214+0800 http-nio-8080-exec-2 WARN anonymous 800x2x1 - ********** /secure/SetupMode!default.jspa [webresource] cyclic plugin resource dependency has been detected with: com.atlassian.plugins.atlassian-plugins-webresource-plugin:context-path, stack trace: [jira.webresources:jira-setup, jira.webresources:_polyfills, com.atlassian.frontend.atlassian-frontend-runtime-plugin:core-js, com.atlassian.plugins.atlassian-plugins-webresource-plugin:context-path, com.atlassian.auiplugin:aui-core, com.atlassian.auiplugin:split_aui.splitchunk.e7c127e2cc]
2025-09-10 13:20:38,215+0800 http-nio-8080-exec-2 WARN anonymous 800x2x1 - ********** /secure/SetupMode!default.jspa [webresource] cyclic plugin resource dependency has been detected with: com.atlassian.plugins.atlassian-plugins-webresource-plugin:context-path, stack trace: [jira.webresources:jira-setup, jira.webresources:_polyfills, com.atlassian.frontend.atlassian-frontend-runtime-plugin:core-js, com.atlassian.plugins.atlassian-plugins-webresource-plugin:context-path, com.atlassian.auiplugin:aui-core, com.atlassian.auiplugin:split_aui.splitchunk.8771ceac91]
2025-09-10 13:20:38,215+0800 http-nio-8080-exec-2 WARN anonymous 800x2x1 - ********** /secure/SetupMode!default.jspa [webresource] cyclic plugin resource dependency has been detected with: com.atlassian.plugins.atlassian-plugins-webresource-plugin:context-path, stack trace: [jira.webresources:jira-setup, jira.webresources:_polyfills, com.atlassian.frontend.atlassian-frontend-runtime-plugin:core-js, com.atlassian.plugins.atlassian-plugins-webresource-plugin:context-path, com.atlassian.auiplugin:aui-core, com.atlassian.auiplugin:split_aui.splitchunk.0fc208f3fe]
2025-09-10 13:20:38,216+0800 http-nio-8080-exec-2 WARN anonymous 800x2x1 - ********** /secure/SetupMode!default.jspa [webresource] cyclic plugin resource dependency has been detected with: com.atlassian.plugins.atlassian-plugins-webresource-plugin:context-path, stack trace: [jira.webresources:jira-setup, jira.webresources:_polyfills, com.atlassian.frontend.atlassian-frontend-runtime-plugin:core-js, com.atlassian.plugins.atlassian-plugins-webresource-plugin:context-path, com.atlassian.auiplugin:aui-core, com.atlassian.auiplugin:split_aui.splitchunk.7873ba9060]
2025-09-10 13:20:38,216+0800 http-nio-8080-exec-2 WARN anonymous 800x2x1 - ********** /secure/SetupMode!default.jspa [webresource] cyclic plugin resource dependency has been detected with: com.atlassian.plugins.atlassian-plugins-webresource-plugin:context-path, stack trace: [jira.webresources:jira-setup, jira.webresources:_polyfills, com.atlassian.frontend.atlassian-frontend-runtime-plugin:core-js, com.atlassian.plugins.atlassian-plugins-webresource-plugin:context-path, com.atlassian.auiplugin:aui-core, com.atlassian.auiplugin:split_aui.splitchunk.2a34183e72]
2025-09-10 13:20:38,216+0800 http-nio-8080-exec-2 WARN anonymous 800x2x1 - ********** /secure/SetupMode!default.jspa [webresource] cyclic plugin resource dependency has been detected with: com.atlassian.plugins.atlassian-plugins-webresource-plugin:context-path, stack trace: [jira.webresources:jira-setup, jira.webresources:_polyfills, com.atlassian.frontend.atlassian-frontend-runtime-plugin:core-js, com.atlassian.plugins.atlassian-plugins-webresource-plugin:context-path, com.atlassian.auiplugin:aui-core, com.atlassian.auiplugin:split_aui.core]
2025-09-10 13:20:38,231+0800 http-nio-8080-exec-2 WARN anonymous 800x2x1 - ********** /secure/SetupMode!default.jspa [c.a.p.webresource.legacy.DefaultResourceDependencyResolver] Cyclic plugin resource dependency has been detected with: com.atlassian.plugins.atlassian-plugins-webresource-plugin:context-path 
    Stack trace: [jira.webresources:jira-setup, jira.webresources:_polyfills, com.atlassian.frontend.atlassian-frontend-runtime-plugin:core-js, com.atlassian.plugins.atlassian-plugins-webresource-plugin:context-path, com.atlassian.auiplugin:aui-core, com.atlassian.auiplugin:common-runtime]
2025-09-10 13:20:38,231+0800 http-nio-8080-exec-2 WARN anonymous 800x2x1 - ********** /secure/SetupMode!default.jspa [c.a.p.webresource.legacy.DefaultResourceDependencyResolver] Cyclic plugin resource dependency has been detected with: com.atlassian.plugins.atlassian-plugins-webresource-plugin:context-path 
    Stack trace: [jira.webresources:jira-setup, jira.webresources:_polyfills, com.atlassian.frontend.atlassian-frontend-runtime-plugin:core-js, com.atlassian.plugins.atlassian-plugins-webresource-plugin:context-path, com.atlassian.auiplugin:aui-core]
2025-09-10 13:20:38,232+0800 http-nio-8080-exec-2 WARN anonymous 800x2x1 - ********** /secure/SetupMode!default.jspa [c.a.p.webresource.legacy.DefaultResourceDependencyResolver] Cyclic plugin resource dependency has been detected with: com.atlassian.plugins.atlassian-plugins-webresource-plugin:context-path 
    Stack trace: [jira.webresources:jira-setup, jira.webresources:_polyfills, com.atlassian.frontend.atlassian-frontend-runtime-plugin:core-js, com.atlassian.plugins.atlassian-plugins-webresource-plugin:context-path, com.atlassian.auiplugin:aui-core, com.atlassian.auiplugin:split_aui.splitchunk.0d131bcbf1]
2025-09-10 13:20:38,232+0800 http-nio-8080-exec-2 WARN anonymous 800x2x1 - ********** /secure/SetupMode!default.jspa [c.a.p.webresource.legacy.DefaultResourceDependencyResolver] Cyclic plugin resource dependency has been detected with: com.atlassian.plugins.atlassian-plugins-webresource-plugin:context-path 
    Stack trace: [jira.webresources:jira-setup, jira.webresources:_polyfills, com.atlassian.frontend.atlassian-frontend-runtime-plugin:core-js, com.atlassian.plugins.atlassian-plugins-webresource-plugin:context-path, com.atlassian.auiplugin:aui-core, com.atlassian.auiplugin:split_aui.splitchunk.444efc83be]
2025-09-10 13:20:38,232+0800 http-nio-8080-exec-2 WARN anonymous 800x2x1 - ********** /secure/SetupMode!default.jspa [c.a.p.webresource.legacy.DefaultResourceDependencyResolver] Cyclic plugin resource dependency has been detected with: com.atlassian.plugins.atlassian-plugins-webresource-plugin:context-path 
    Stack trace: [jira.webresources:jira-setup, jira.webresources:_polyfills, com.atlassian.frontend.atlassian-frontend-runtime-plugin:core-js, com.atlassian.plugins.atlassian-plugins-webresource-plugin:context-path, com.atlassian.auiplugin:aui-core, com.atlassian.auiplugin:split_aui.splitchunk.78b1c5d416]
2025-09-10 13:20:38,233+0800 http-nio-8080-exec-2 WARN anonymous 800x2x1 - ********** /secure/SetupMode!default.jspa [c.a.p.webresource.legacy.DefaultResourceDependencyResolver] Cyclic plugin resource dependency has been detected with: com.atlassian.plugins.atlassian-plugins-webresource-plugin:context-path 
    Stack trace: [jira.webresources:jira-setup, jira.webresources:_polyfills, com.atlassian.frontend.atlassian-frontend-runtime-plugin:core-js, com.atlassian.plugins.atlassian-plugins-webresource-plugin:context-path, com.atlassian.auiplugin:aui-core, com.atlassian.auiplugin:split_aui.splitchunk.9bbf750f52]
2025-09-10 13:20:38,233+0800 http-nio-8080-exec-2 WARN anonymous 800x2x1 - ********** /secure/SetupMode!default.jspa [c.a.p.webresource.legacy.DefaultResourceDependencyResolver] Cyclic plugin resource dependency has been detected with: com.atlassian.plugins.atlassian-plugins-webresource-plugin:context-path 
    Stack trace: [jira.webresources:jira-setup, jira.webresources:_polyfills, com.atlassian.frontend.atlassian-frontend-runtime-plugin:core-js, com.atlassian.plugins.atlassian-plugins-webresource-plugin:context-path, com.atlassian.auiplugin:aui-core, com.atlassian.auiplugin:split_aui.splitchunk.479fe6ee76]
2025-09-10 13:20:38,233+0800 http-nio-8080-exec-2 WARN anonymous 800x2x1 - ********** /secure/SetupMode!default.jspa [c.a.p.webresource.legacy.DefaultResourceDependencyResolver] Cyclic plugin resource dependency has been detected with: com.atlassian.plugins.atlassian-plugins-webresource-plugin:context-path 
    Stack trace: [jira.webresources:jira-setup, jira.webresources:_polyfills, com.atlassian.frontend.atlassian-frontend-runtime-plugin:core-js, com.atlassian.plugins.atlassian-plugins-webresource-plugin:context-path, com.atlassian.auiplugin:aui-core, com.atlassian.auiplugin:split_aui.splitchunk.e7c127e2cc]
2025-09-10 13:20:38,234+0800 http-nio-8080-exec-2 WARN anonymous 800x2x1 - ********** /secure/SetupMode!default.jspa [c.a.p.webresource.legacy.DefaultResourceDependencyResolver] Cyclic plugin resource dependency has been detected with: com.atlassian.plugins.atlassian-plugins-webresource-plugin:context-path 
    Stack trace: [jira.webresources:jira-setup, jira.webresources:_polyfills, com.atlassian.frontend.atlassian-frontend-runtime-plugin:core-js, com.atlassian.plugins.atlassian-plugins-webresource-plugin:context-path, com.atlassian.auiplugin:aui-core, com.atlassian.auiplugin:split_aui.splitchunk.8771ceac91]
2025-09-10 13:20:38,234+0800 http-nio-8080-exec-2 WARN anonymous 800x2x1 - ********** /secure/SetupMode!default.jspa [c.a.p.webresource.legacy.DefaultResourceDependencyResolver] Cyclic plugin resource dependency has been detected with: com.atlassian.plugins.atlassian-plugins-webresource-plugin:context-path 
    Stack trace: [jira.webresources:jira-setup, jira.webresources:_polyfills, com.atlassian.frontend.atlassian-frontend-runtime-plugin:core-js, com.atlassian.plugins.atlassian-plugins-webresource-plugin:context-path, com.atlassian.auiplugin:aui-core, com.atlassian.auiplugin:split_aui.splitchunk.0fc208f3fe]
2025-09-10 13:20:38,234+0800 http-nio-8080-exec-2 WARN anonymous 800x2x1 - ********** /secure/SetupMode!default.jspa [c.a.p.webresource.legacy.DefaultResourceDependencyResolver] Cyclic plugin resource dependency has been detected with: com.atlassian.plugins.atlassian-plugins-webresource-plugin:context-path 
    Stack trace: [jira.webresources:jira-setup, jira.webresources:_polyfills, com.atlassian.frontend.atlassian-frontend-runtime-plugin:core-js, com.atlassian.plugins.atlassian-plugins-webresource-plugin:context-path, com.atlassian.auiplugin:aui-core, com.atlassian.auiplugin:split_aui.splitchunk.7873ba9060]
2025-09-10 13:20:38,234+0800 http-nio-8080-exec-2 WARN anonymous 800x2x1 - ********** /secure/SetupMode!default.jspa [c.a.p.webresource.legacy.DefaultResourceDependencyResolver] Cyclic plugin resource dependency has been detected with: com.atlassian.plugins.atlassian-plugins-webresource-plugin:context-path 
    Stack trace: [jira.webresources:jira-setup, jira.webresources:_polyfills, com.atlassian.frontend.atlassian-frontend-runtime-plugin:core-js, com.atlassian.plugins.atlassian-plugins-webresource-plugin:context-path, com.atlassian.auiplugin:aui-core, com.atlassian.auiplugin:split_aui.splitchunk.2a34183e72]
2025-09-10 13:20:38,235+0800 http-nio-8080-exec-2 WARN anonymous 800x2x1 - ********** /secure/SetupMode!default.jspa [c.a.p.webresource.legacy.DefaultResourceDependencyResolver] Cyclic plugin resource dependency has been detected with: com.atlassian.plugins.atlassian-plugins-webresource-plugin:context-path 
    Stack trace: [jira.webresources:jira-setup, jira.webresources:_polyfills, com.atlassian.frontend.atlassian-frontend-runtime-plugin:core-js, com.atlassian.plugins.atlassian-plugins-webresource-plugin:context-path, com.atlassian.auiplugin:aui-core, com.atlassian.auiplugin:split_aui.core]
2025-09-10 13:20:38,253+0800 http-nio-8080-exec-2 WARN anonymous 800x2x1 - ********** /secure/SetupMode!default.jspa [webresource] cyclic plugin resource dependency has been detected with: com.atlassian.plugins.atlassian-plugins-webresource-plugin:context-path, stack trace: [jira.webresources:jira-setup, jira.webresources:_polyfills, com.atlassian.frontend.atlassian-frontend-runtime-plugin:core-js, com.atlassian.plugins.atlassian-plugins-webresource-plugin:context-path, com.atlassian.auiplugin:aui-core, com.atlassian.auiplugin:common-runtime]
2025-09-10 13:20:38,257+0800 http-nio-8080-exec-2 WARN anonymous 800x2x1 - ********** /secure/SetupMode!default.jspa [webresource] cyclic plugin resource dependency has been detected with: com.atlassian.plugins.atlassian-plugins-webresource-plugin:context-path, stack trace: [jira.webresources:jira-setup, jira.webresources:_polyfills, com.atlassian.frontend.atlassian-frontend-runtime-plugin:core-js, com.atlassian.plugins.atlassian-plugins-webresource-plugin:context-path, com.atlassian.auiplugin:aui-core]
2025-09-10 13:20:38,259+0800 http-nio-8080-exec-2 WARN anonymous 800x2x1 - ********** /secure/SetupMode!default.jspa [webresource] cyclic plugin resource dependency has been detected with: com.atlassian.plugins.atlassian-plugins-webresource-plugin:context-path, stack trace: [jira.webresources:jira-setup, jira.webresources:_polyfills, com.atlassian.frontend.atlassian-frontend-runtime-plugin:core-js, com.atlassian.plugins.atlassian-plugins-webresource-plugin:context-path, com.atlassian.auiplugin:aui-core, com.atlassian.auiplugin:split_aui.splitchunk.0d131bcbf1]
2025-09-10 13:20:38,260+0800 http-nio-8080-exec-2 WARN anonymous 800x2x1 - ********** /secure/SetupMode!default.jspa [webresource] cyclic plugin resource dependency has been detected with: com.atlassian.plugins.atlassian-plugins-webresource-plugin:context-path, stack trace: [jira.webresources:jira-setup, jira.webresources:_polyfills, com.atlassian.frontend.atlassian-frontend-runtime-plugin:core-js, com.atlassian.plugins.atlassian-plugins-webresource-plugin:context-path, com.atlassian.auiplugin:aui-core, com.atlassian.auiplugin:split_aui.splitchunk.444efc83be]
2025-09-10 13:20:38,261+0800 http-nio-8080-exec-2 WARN anonymous 800x2x1 - ********** /secure/SetupMode!default.jspa [webresource] cyclic plugin resource dependency has been detected with: com.atlassian.plugins.atlassian-plugins-webresource-plugin:context-path, stack trace: [jira.webresources:jira-setup, jira.webresources:_polyfills, com.atlassian.frontend.atlassian-frontend-runtime-plugin:core-js, com.atlassian.plugins.atlassian-plugins-webresource-plugin:context-path, com.atlassian.auiplugin:aui-core, com.atlassian.auiplugin:split_aui.splitchunk.78b1c5d416]
2025-09-10 13:20:38,261+0800 http-nio-8080-exec-2 WARN anonymous 800x2x1 - ********** /secure/SetupMode!default.jspa [webresource] cyclic plugin resource dependency has been detected with: com.atlassian.plugins.atlassian-plugins-webresource-plugin:context-path, stack trace: [jira.webresources:jira-setup, jira.webresources:_polyfills, com.atlassian.frontend.atlassian-frontend-runtime-plugin:core-js, com.atlassian.plugins.atlassian-plugins-webresource-plugin:context-path, com.atlassian.auiplugin:aui-core, com.atlassian.auiplugin:split_aui.splitchunk.9bbf750f52]
2025-09-10 13:20:38,261+0800 http-nio-8080-exec-2 WARN anonymous 800x2x1 - ********** /secure/SetupMode!default.jspa [webresource] cyclic plugin resource dependency has been detected with: com.atlassian.plugins.atlassian-plugins-webresource-plugin:context-path, stack trace: [jira.webresources:jira-setup, jira.webresources:_polyfills, com.atlassian.frontend.atlassian-frontend-runtime-plugin:core-js, com.atlassian.plugins.atlassian-plugins-webresource-plugin:context-path, com.atlassian.auiplugin:aui-core, com.atlassian.auiplugin:split_aui.splitchunk.479fe6ee76]
2025-09-10 13:20:38,262+0800 http-nio-8080-exec-2 WARN anonymous 800x2x1 - ********** /secure/SetupMode!default.jspa [webresource] cyclic plugin resource dependency has been detected with: com.atlassian.plugins.atlassian-plugins-webresource-plugin:context-path, stack trace: [jira.webresources:jira-setup, jira.webresources:_polyfills, com.atlassian.frontend.atlassian-frontend-runtime-plugin:core-js, com.atlassian.plugins.atlassian-plugins-webresource-plugin:context-path, com.atlassian.auiplugin:aui-core, com.atlassian.auiplugin:split_aui.splitchunk.e7c127e2cc]
2025-09-10 13:20:38,262+0800 http-nio-8080-exec-2 WARN anonymous 800x2x1 - ********** /secure/SetupMode!default.jspa [webresource] cyclic plugin resource dependency has been detected with: com.atlassian.plugins.atlassian-plugins-webresource-plugin:context-path, stack trace: [jira.webresources:jira-setup, jira.webresources:_polyfills, com.atlassian.frontend.atlassian-frontend-runtime-plugin:core-js, com.atlassian.plugins.atlassian-plugins-webresource-plugin:context-path, com.atlassian.auiplugin:aui-core, com.atlassian.auiplugin:split_aui.splitchunk.8771ceac91]
2025-09-10 13:20:38,262+0800 http-nio-8080-exec-2 WARN anonymous 800x2x1 - ********** /secure/SetupMode!default.jspa [webresource] cyclic plugin resource dependency has been detected with: com.atlassian.plugins.atlassian-plugins-webresource-plugin:context-path, stack trace: [jira.webresources:jira-setup, jira.webresources:_polyfills, com.atlassian.frontend.atlassian-frontend-runtime-plugin:core-js, com.atlassian.plugins.atlassian-plugins-webresource-plugin:context-path, com.atlassian.auiplugin:aui-core, com.atlassian.auiplugin:split_aui.splitchunk.0fc208f3fe]
2025-09-10 13:20:38,262+0800 http-nio-8080-exec-2 WARN anonymous 800x2x1 - ********** /secure/SetupMode!default.jspa [webresource] cyclic plugin resource dependency has been detected with: com.atlassian.plugins.atlassian-plugins-webresource-plugin:context-path, stack trace: [jira.webresources:jira-setup, jira.webresources:_polyfills, com.atlassian.frontend.atlassian-frontend-runtime-plugin:core-js, com.atlassian.plugins.atlassian-plugins-webresource-plugin:context-path, com.atlassian.auiplugin:aui-core, com.atlassian.auiplugin:split_aui.splitchunk.7873ba9060]
2025-09-10 13:20:38,262+0800 http-nio-8080-exec-2 WARN anonymous 800x2x1 - ********** /secure/SetupMode!default.jspa [webresource] cyclic plugin resource dependency has been detected with: com.atlassian.plugins.atlassian-plugins-webresource-plugin:context-path, stack trace: [jira.webresources:jira-setup, jira.webresources:_polyfills, com.atlassian.frontend.atlassian-frontend-runtime-plugin:core-js, com.atlassian.plugins.atlassian-plugins-webresource-plugin:context-path, com.atlassian.auiplugin:aui-core, com.atlassian.auiplugin:split_aui.splitchunk.2a34183e72]
2025-09-10 13:20:38,262+0800 http-nio-8080-exec-2 WARN anonymous 800x2x1 - ********** /secure/SetupMode!default.jspa [webresource] cyclic plugin resource dependency has been detected with: com.atlassian.plugins.atlassian-plugins-webresource-plugin:context-path, stack trace: [jira.webresources:jira-setup, jira.webresources:_polyfills, com.atlassian.frontend.atlassian-frontend-runtime-plugin:core-js, com.atlassian.plugins.atlassian-plugins-webresource-plugin:context-path, com.atlassian.auiplugin:aui-core, com.atlassian.auiplugin:split_aui.core]
2025-09-10 13:20:44,265+0800 http-nio-8080-exec-7 WARN anonymous 800x7x1 19bn0kh ********** /secure/SetupMode!default.jspa [webresource] cyclic plugin resource dependency has been detected with: com.atlassian.plugins.atlassian-plugins-webresource-plugin:context-path, stack trace: [jira.webresources:jira-setup, jira.webresources:_polyfills, com.atlassian.frontend.atlassian-frontend-runtime-plugin:core-js, com.atlassian.plugins.atlassian-plugins-webresource-plugin:context-path, com.atlassian.auiplugin:aui-core, com.atlassian.auiplugin:common-runtime]
2025-09-10 13:20:44,266+0800 http-nio-8080-exec-7 WARN anonymous 800x7x1 19bn0kh ********** /secure/SetupMode!default.jspa [webresource] cyclic plugin resource dependency has been detected with: com.atlassian.plugins.atlassian-plugins-webresource-plugin:context-path, stack trace: [jira.webresources:jira-setup, jira.webresources:_polyfills, com.atlassian.frontend.atlassian-frontend-runtime-plugin:core-js, com.atlassian.plugins.atlassian-plugins-webresource-plugin:context-path, com.atlassian.auiplugin:aui-core]
2025-09-10 13:20:44,266+0800 http-nio-8080-exec-7 WARN anonymous 800x7x1 19bn0kh ********** /secure/SetupMode!default.jspa [webresource] cyclic plugin resource dependency has been detected with: com.atlassian.plugins.atlassian-plugins-webresource-plugin:context-path, stack trace: [jira.webresources:jira-setup, jira.webresources:_polyfills, com.atlassian.frontend.atlassian-frontend-runtime-plugin:core-js, com.atlassian.plugins.atlassian-plugins-webresource-plugin:context-path, com.atlassian.auiplugin:aui-core, com.atlassian.auiplugin:split_aui.splitchunk.0d131bcbf1]
2025-09-10 13:20:44,266+0800 http-nio-8080-exec-7 WARN anonymous 800x7x1 19bn0kh ********** /secure/SetupMode!default.jspa [webresource] cyclic plugin resource dependency has been detected with: com.atlassian.plugins.atlassian-plugins-webresource-plugin:context-path, stack trace: [jira.webresources:jira-setup, jira.webresources:_polyfills, com.atlassian.frontend.atlassian-frontend-runtime-plugin:core-js, com.atlassian.plugins.atlassian-plugins-webresource-plugin:context-path, com.atlassian.auiplugin:aui-core, com.atlassian.auiplugin:split_aui.splitchunk.444efc83be]
2025-09-10 13:20:44,266+0800 http-nio-8080-exec-7 WARN anonymous 800x7x1 19bn0kh ********** /secure/SetupMode!default.jspa [webresource] cyclic plugin resource dependency has been detected with: com.atlassian.plugins.atlassian-plugins-webresource-plugin:context-path, stack trace: [jira.webresources:jira-setup, jira.webresources:_polyfills, com.atlassian.frontend.atlassian-frontend-runtime-plugin:core-js, com.atlassian.plugins.atlassian-plugins-webresource-plugin:context-path, com.atlassian.auiplugin:aui-core, com.atlassian.auiplugin:split_aui.splitchunk.78b1c5d416]
2025-09-10 13:20:44,267+0800 http-nio-8080-exec-7 WARN anonymous 800x7x1 19bn0kh ********** /secure/SetupMode!default.jspa [webresource] cyclic plugin resource dependency has been detected with: com.atlassian.plugins.atlassian-plugins-webresource-plugin:context-path, stack trace: [jira.webresources:jira-setup, jira.webresources:_polyfills, com.atlassian.frontend.atlassian-frontend-runtime-plugin:core-js, com.atlassian.plugins.atlassian-plugins-webresource-plugin:context-path, com.atlassian.auiplugin:aui-core, com.atlassian.auiplugin:split_aui.splitchunk.9bbf750f52]
2025-09-10 13:20:44,267+0800 http-nio-8080-exec-7 WARN anonymous 800x7x1 19bn0kh ********** /secure/SetupMode!default.jspa [webresource] cyclic plugin resource dependency has been detected with: com.atlassian.plugins.atlassian-plugins-webresource-plugin:context-path, stack trace: [jira.webresources:jira-setup, jira.webresources:_polyfills, com.atlassian.frontend.atlassian-frontend-runtime-plugin:core-js, com.atlassian.plugins.atlassian-plugins-webresource-plugin:context-path, com.atlassian.auiplugin:aui-core, com.atlassian.auiplugin:split_aui.splitchunk.479fe6ee76]
2025-09-10 13:20:44,267+0800 http-nio-8080-exec-7 WARN anonymous 800x7x1 19bn0kh ********** /secure/SetupMode!default.jspa [webresource] cyclic plugin resource dependency has been detected with: com.atlassian.plugins.atlassian-plugins-webresource-plugin:context-path, stack trace: [jira.webresources:jira-setup, jira.webresources:_polyfills, com.atlassian.frontend.atlassian-frontend-runtime-plugin:core-js, com.atlassian.plugins.atlassian-plugins-webresource-plugin:context-path, com.atlassian.auiplugin:aui-core, com.atlassian.auiplugin:split_aui.splitchunk.e7c127e2cc]
2025-09-10 13:20:44,267+0800 http-nio-8080-exec-7 WARN anonymous 800x7x1 19bn0kh ********** /secure/SetupMode!default.jspa [webresource] cyclic plugin resource dependency has been detected with: com.atlassian.plugins.atlassian-plugins-webresource-plugin:context-path, stack trace: [jira.webresources:jira-setup, jira.webresources:_polyfills, com.atlassian.frontend.atlassian-frontend-runtime-plugin:core-js, com.atlassian.plugins.atlassian-plugins-webresource-plugin:context-path, com.atlassian.auiplugin:aui-core, com.atlassian.auiplugin:split_aui.splitchunk.8771ceac91]
2025-09-10 13:20:44,267+0800 http-nio-8080-exec-7 WARN anonymous 800x7x1 19bn0kh ********** /secure/SetupMode!default.jspa [webresource] cyclic plugin resource dependency has been detected with: com.atlassian.plugins.atlassian-plugins-webresource-plugin:context-path, stack trace: [jira.webresources:jira-setup, jira.webresources:_polyfills, com.atlassian.frontend.atlassian-frontend-runtime-plugin:core-js, com.atlassian.plugins.atlassian-plugins-webresource-plugin:context-path, com.atlassian.auiplugin:aui-core, com.atlassian.auiplugin:split_aui.splitchunk.0fc208f3fe]
2025-09-10 13:20:44,267+0800 http-nio-8080-exec-7 WARN anonymous 800x7x1 19bn0kh ********** /secure/SetupMode!default.jspa [webresource] cyclic plugin resource dependency has been detected with: com.atlassian.plugins.atlassian-plugins-webresource-plugin:context-path, stack trace: [jira.webresources:jira-setup, jira.webresources:_polyfills, com.atlassian.frontend.atlassian-frontend-runtime-plugin:core-js, com.atlassian.plugins.atlassian-plugins-webresource-plugin:context-path, com.atlassian.auiplugin:aui-core, com.atlassian.auiplugin:split_aui.splitchunk.7873ba9060]
2025-09-10 13:20:44,268+0800 http-nio-8080-exec-7 WARN anonymous 800x7x1 19bn0kh ********** /secure/SetupMode!default.jspa [webresource] cyclic plugin resource dependency has been detected with: com.atlassian.plugins.atlassian-plugins-webresource-plugin:context-path, stack trace: [jira.webresources:jira-setup, jira.webresources:_polyfills, com.atlassian.frontend.atlassian-frontend-runtime-plugin:core-js, com.atlassian.plugins.atlassian-plugins-webresource-plugin:context-path, com.atlassian.auiplugin:aui-core, com.atlassian.auiplugin:split_aui.splitchunk.2a34183e72]
2025-09-10 13:20:44,268+0800 http-nio-8080-exec-7 WARN anonymous 800x7x1 19bn0kh ********** /secure/SetupMode!default.jspa [webresource] cyclic plugin resource dependency has been detected with: com.atlassian.plugins.atlassian-plugins-webresource-plugin:context-path, stack trace: [jira.webresources:jira-setup, jira.webresources:_polyfills, com.atlassian.frontend.atlassian-frontend-runtime-plugin:core-js, com.atlassian.plugins.atlassian-plugins-webresource-plugin:context-path, com.atlassian.auiplugin:aui-core, com.atlassian.auiplugin:split_aui.core]
2025-09-10 13:20:44,268+0800 http-nio-8080-exec-7 WARN anonymous 800x7x1 19bn0kh ********** /secure/SetupMode!default.jspa [webresource] cyclic plugin resource dependency has been detected with: com.atlassian.plugins.atlassian-plugins-webresource-plugin:context-path, stack trace: [jira.webresources:jira-setup, jira.webresources:_polyfills, com.atlassian.frontend.atlassian-frontend-runtime-plugin:core-js, com.atlassian.plugins.atlassian-plugins-webresource-plugin:context-path, com.atlassian.auiplugin:aui-core, com.atlassian.auiplugin:common-runtime]
2025-09-10 13:20:44,268+0800 http-nio-8080-exec-7 WARN anonymous 800x7x1 19bn0kh ********** /secure/SetupMode!default.jspa [webresource] cyclic plugin resource dependency has been detected with: com.atlassian.plugins.atlassian-plugins-webresource-plugin:context-path, stack trace: [jira.webresources:jira-setup, jira.webresources:_polyfills, com.atlassian.frontend.atlassian-frontend-runtime-plugin:core-js, com.atlassian.plugins.atlassian-plugins-webresource-plugin:context-path, com.atlassian.auiplugin:aui-core]
2025-09-10 13:20:44,269+0800 http-nio-8080-exec-7 WARN anonymous 800x7x1 19bn0kh ********** /secure/SetupMode!default.jspa [webresource] cyclic plugin resource dependency has been detected with: com.atlassian.plugins.atlassian-plugins-webresource-plugin:context-path, stack trace: [jira.webresources:jira-setup, jira.webresources:_polyfills, com.atlassian.frontend.atlassian-frontend-runtime-plugin:core-js, com.atlassian.plugins.atlassian-plugins-webresource-plugin:context-path, com.atlassian.auiplugin:aui-core, com.atlassian.auiplugin:split_aui.splitchunk.0d131bcbf1]
2025-09-10 13:20:44,269+0800 http-nio-8080-exec-7 WARN anonymous 800x7x1 19bn0kh ********** /secure/SetupMode!default.jspa [webresource] cyclic plugin resource dependency has been detected with: com.atlassian.plugins.atlassian-plugins-webresource-plugin:context-path, stack trace: [jira.webresources:jira-setup, jira.webresources:_polyfills, com.atlassian.frontend.atlassian-frontend-runtime-plugin:core-js, com.atlassian.plugins.atlassian-plugins-webresource-plugin:context-path, com.atlassian.auiplugin:aui-core, com.atlassian.auiplugin:split_aui.splitchunk.444efc83be]
2025-09-10 13:20:44,269+0800 http-nio-8080-exec-7 WARN anonymous 800x7x1 19bn0kh ********** /secure/SetupMode!default.jspa [webresource] cyclic plugin resource dependency has been detected with: com.atlassian.plugins.atlassian-plugins-webresource-plugin:context-path, stack trace: [jira.webresources:jira-setup, jira.webresources:_polyfills, com.atlassian.frontend.atlassian-frontend-runtime-plugin:core-js, com.atlassian.plugins.atlassian-plugins-webresource-plugin:context-path, com.atlassian.auiplugin:aui-core, com.atlassian.auiplugin:split_aui.splitchunk.78b1c5d416]
2025-09-10 13:20:44,269+0800 http-nio-8080-exec-7 WARN anonymous 800x7x1 19bn0kh ********** /secure/SetupMode!default.jspa [webresource] cyclic plugin resource dependency has been detected with: com.atlassian.plugins.atlassian-plugins-webresource-plugin:context-path, stack trace: [jira.webresources:jira-setup, jira.webresources:_polyfills, com.atlassian.frontend.atlassian-frontend-runtime-plugin:core-js, com.atlassian.plugins.atlassian-plugins-webresource-plugin:context-path, com.atlassian.auiplugin:aui-core, com.atlassian.auiplugin:split_aui.splitchunk.9bbf750f52]
2025-09-10 13:20:44,269+0800 http-nio-8080-exec-7 WARN anonymous 800x7x1 19bn0kh ********** /secure/SetupMode!default.jspa [webresource] cyclic plugin resource dependency has been detected with: com.atlassian.plugins.atlassian-plugins-webresource-plugin:context-path, stack trace: [jira.webresources:jira-setup, jira.webresources:_polyfills, com.atlassian.frontend.atlassian-frontend-runtime-plugin:core-js, com.atlassian.plugins.atlassian-plugins-webresource-plugin:context-path, com.atlassian.auiplugin:aui-core, com.atlassian.auiplugin:split_aui.splitchunk.479fe6ee76]
2025-09-10 13:20:44,269+0800 http-nio-8080-exec-7 WARN anonymous 800x7x1 19bn0kh ********** /secure/SetupMode!default.jspa [webresource] cyclic plugin resource dependency has been detected with: com.atlassian.plugins.atlassian-plugins-webresource-plugin:context-path, stack trace: [jira.webresources:jira-setup, jira.webresources:_polyfills, com.atlassian.frontend.atlassian-frontend-runtime-plugin:core-js, com.atlassian.plugins.atlassian-plugins-webresource-plugin:context-path, com.atlassian.auiplugin:aui-core, com.atlassian.auiplugin:split_aui.splitchunk.e7c127e2cc]
2025-09-10 13:20:44,270+0800 http-nio-8080-exec-7 WARN anonymous 800x7x1 19bn0kh ********** /secure/SetupMode!default.jspa [webresource] cyclic plugin resource dependency has been detected with: com.atlassian.plugins.atlassian-plugins-webresource-plugin:context-path, stack trace: [jira.webresources:jira-setup, jira.webresources:_polyfills, com.atlassian.frontend.atlassian-frontend-runtime-plugin:core-js, com.atlassian.plugins.atlassian-plugins-webresource-plugin:context-path, com.atlassian.auiplugin:aui-core, com.atlassian.auiplugin:split_aui.splitchunk.8771ceac91]
2025-09-10 13:20:44,270+0800 http-nio-8080-exec-7 WARN anonymous 800x7x1 19bn0kh ********** /secure/SetupMode!default.jspa [webresource] cyclic plugin resource dependency has been detected with: com.atlassian.plugins.atlassian-plugins-webresource-plugin:context-path, stack trace: [jira.webresources:jira-setup, jira.webresources:_polyfills, com.atlassian.frontend.atlassian-frontend-runtime-plugin:core-js, com.atlassian.plugins.atlassian-plugins-webresource-plugin:context-path, com.atlassian.auiplugin:aui-core, com.atlassian.auiplugin:split_aui.splitchunk.0fc208f3fe]
2025-09-10 13:20:44,270+0800 http-nio-8080-exec-7 WARN anonymous 800x7x1 19bn0kh ********** /secure/SetupMode!default.jspa [webresource] cyclic plugin resource dependency has been detected with: com.atlassian.plugins.atlassian-plugins-webresource-plugin:context-path, stack trace: [jira.webresources:jira-setup, jira.webresources:_polyfills, com.atlassian.frontend.atlassian-frontend-runtime-plugin:core-js, com.atlassian.plugins.atlassian-plugins-webresource-plugin:context-path, com.atlassian.auiplugin:aui-core, com.atlassian.auiplugin:split_aui.splitchunk.7873ba9060]
2025-09-10 13:20:44,270+0800 http-nio-8080-exec-7 WARN anonymous 800x7x1 19bn0kh ********** /secure/SetupMode!default.jspa [webresource] cyclic plugin resource dependency has been detected with: com.atlassian.plugins.atlassian-plugins-webresource-plugin:context-path, stack trace: [jira.webresources:jira-setup, jira.webresources:_polyfills, com.atlassian.frontend.atlassian-frontend-runtime-plugin:core-js, com.atlassian.plugins.atlassian-plugins-webresource-plugin:context-path, com.atlassian.auiplugin:aui-core, com.atlassian.auiplugin:split_aui.splitchunk.2a34183e72]
2025-09-10 13:20:44,270+0800 http-nio-8080-exec-7 WARN anonymous 800x7x1 19bn0kh ********** /secure/SetupMode!default.jspa [webresource] cyclic plugin resource dependency has been detected with: com.atlassian.plugins.atlassian-plugins-webresource-plugin:context-path, stack trace: [jira.webresources:jira-setup, jira.webresources:_polyfills, com.atlassian.frontend.atlassian-frontend-runtime-plugin:core-js, com.atlassian.plugins.atlassian-plugins-webresource-plugin:context-path, com.atlassian.auiplugin:aui-core, com.atlassian.auiplugin:split_aui.core]
2025-09-10 13:20:46,381+0800 http-nio-8080-exec-10 WARN anonymous 800x10x1 19bn0kh ********** /secure/SetupDatabase!default.jspa [webresource] cyclic plugin resource dependency has been detected with: com.atlassian.plugins.atlassian-plugins-webresource-plugin:context-path, stack trace: [jira.webresources:jira-setup, jira.webresources:_polyfills, com.atlassian.frontend.atlassian-frontend-runtime-plugin:core-js, com.atlassian.plugins.atlassian-plugins-webresource-plugin:context-path, com.atlassian.auiplugin:aui-core, com.atlassian.auiplugin:common-runtime]
2025-09-10 13:20:46,382+0800 http-nio-8080-exec-10 WARN anonymous 800x10x1 19bn0kh ********** /secure/SetupDatabase!default.jspa [webresource] cyclic plugin resource dependency has been detected with: com.atlassian.plugins.atlassian-plugins-webresource-plugin:context-path, stack trace: [jira.webresources:jira-setup, jira.webresources:_polyfills, com.atlassian.frontend.atlassian-frontend-runtime-plugin:core-js, com.atlassian.plugins.atlassian-plugins-webresource-plugin:context-path, com.atlassian.auiplugin:aui-core]
2025-09-10 13:20:46,382+0800 http-nio-8080-exec-10 WARN anonymous 800x10x1 19bn0kh ********** /secure/SetupDatabase!default.jspa [webresource] cyclic plugin resource dependency has been detected with: com.atlassian.plugins.atlassian-plugins-webresource-plugin:context-path, stack trace: [jira.webresources:jira-setup, jira.webresources:_polyfills, com.atlassian.frontend.atlassian-frontend-runtime-plugin:core-js, com.atlassian.plugins.atlassian-plugins-webresource-plugin:context-path, com.atlassian.auiplugin:aui-core, com.atlassian.auiplugin:split_aui.splitchunk.0d131bcbf1]
2025-09-10 13:20:46,382+0800 http-nio-8080-exec-10 WARN anonymous 800x10x1 19bn0kh ********** /secure/SetupDatabase!default.jspa [webresource] cyclic plugin resource dependency has been detected with: com.atlassian.plugins.atlassian-plugins-webresource-plugin:context-path, stack trace: [jira.webresources:jira-setup, jira.webresources:_polyfills, com.atlassian.frontend.atlassian-frontend-runtime-plugin:core-js, com.atlassian.plugins.atlassian-plugins-webresource-plugin:context-path, com.atlassian.auiplugin:aui-core, com.atlassian.auiplugin:split_aui.splitchunk.444efc83be]
2025-09-10 13:20:46,383+0800 http-nio-8080-exec-10 WARN anonymous 800x10x1 19bn0kh ********** /secure/SetupDatabase!default.jspa [webresource] cyclic plugin resource dependency has been detected with: com.atlassian.plugins.atlassian-plugins-webresource-plugin:context-path, stack trace: [jira.webresources:jira-setup, jira.webresources:_polyfills, com.atlassian.frontend.atlassian-frontend-runtime-plugin:core-js, com.atlassian.plugins.atlassian-plugins-webresource-plugin:context-path, com.atlassian.auiplugin:aui-core, com.atlassian.auiplugin:split_aui.splitchunk.78b1c5d416]
2025-09-10 13:20:46,383+0800 http-nio-8080-exec-10 WARN anonymous 800x10x1 19bn0kh ********** /secure/SetupDatabase!default.jspa [webresource] cyclic plugin resource dependency has been detected with: com.atlassian.plugins.atlassian-plugins-webresource-plugin:context-path, stack trace: [jira.webresources:jira-setup, jira.webresources:_polyfills, com.atlassian.frontend.atlassian-frontend-runtime-plugin:core-js, com.atlassian.plugins.atlassian-plugins-webresource-plugin:context-path, com.atlassian.auiplugin:aui-core, com.atlassian.auiplugin:split_aui.splitchunk.9bbf750f52]
2025-09-10 13:20:46,384+0800 http-nio-8080-exec-10 WARN anonymous 800x10x1 19bn0kh ********** /secure/SetupDatabase!default.jspa [webresource] cyclic plugin resource dependency has been detected with: com.atlassian.plugins.atlassian-plugins-webresource-plugin:context-path, stack trace: [jira.webresources:jira-setup, jira.webresources:_polyfills, com.atlassian.frontend.atlassian-frontend-runtime-plugin:core-js, com.atlassian.plugins.atlassian-plugins-webresource-plugin:context-path, com.atlassian.auiplugin:aui-core, com.atlassian.auiplugin:split_aui.splitchunk.479fe6ee76]
2025-09-10 13:20:46,384+0800 http-nio-8080-exec-10 WARN anonymous 800x10x1 19bn0kh ********** /secure/SetupDatabase!default.jspa [webresource] cyclic plugin resource dependency has been detected with: com.atlassian.plugins.atlassian-plugins-webresource-plugin:context-path, stack trace: [jira.webresources:jira-setup, jira.webresources:_polyfills, com.atlassian.frontend.atlassian-frontend-runtime-plugin:core-js, com.atlassian.plugins.atlassian-plugins-webresource-plugin:context-path, com.atlassian.auiplugin:aui-core, com.atlassian.auiplugin:split_aui.splitchunk.e7c127e2cc]
2025-09-10 13:20:46,385+0800 http-nio-8080-exec-10 WARN anonymous 800x10x1 19bn0kh ********** /secure/SetupDatabase!default.jspa [webresource] cyclic plugin resource dependency has been detected with: com.atlassian.plugins.atlassian-plugins-webresource-plugin:context-path, stack trace: [jira.webresources:jira-setup, jira.webresources:_polyfills, com.atlassian.frontend.atlassian-frontend-runtime-plugin:core-js, com.atlassian.plugins.atlassian-plugins-webresource-plugin:context-path, com.atlassian.auiplugin:aui-core, com.atlassian.auiplugin:split_aui.splitchunk.8771ceac91]
2025-09-10 13:20:46,385+0800 http-nio-8080-exec-10 WARN anonymous 800x10x1 19bn0kh ********** /secure/SetupDatabase!default.jspa [webresource] cyclic plugin resource dependency has been detected with: com.atlassian.plugins.atlassian-plugins-webresource-plugin:context-path, stack trace: [jira.webresources:jira-setup, jira.webresources:_polyfills, com.atlassian.frontend.atlassian-frontend-runtime-plugin:core-js, com.atlassian.plugins.atlassian-plugins-webresource-plugin:context-path, com.atlassian.auiplugin:aui-core, com.atlassian.auiplugin:split_aui.splitchunk.0fc208f3fe]
2025-09-10 13:20:46,385+0800 http-nio-8080-exec-10 WARN anonymous 800x10x1 19bn0kh ********** /secure/SetupDatabase!default.jspa [webresource] cyclic plugin resource dependency has been detected with: com.atlassian.plugins.atlassian-plugins-webresource-plugin:context-path, stack trace: [jira.webresources:jira-setup, jira.webresources:_polyfills, com.atlassian.frontend.atlassian-frontend-runtime-plugin:core-js, com.atlassian.plugins.atlassian-plugins-webresource-plugin:context-path, com.atlassian.auiplugin:aui-core, com.atlassian.auiplugin:split_aui.splitchunk.7873ba9060]
2025-09-10 13:20:46,386+0800 http-nio-8080-exec-10 WARN anonymous 800x10x1 19bn0kh ********** /secure/SetupDatabase!default.jspa [webresource] cyclic plugin resource dependency has been detected with: com.atlassian.plugins.atlassian-plugins-webresource-plugin:context-path, stack trace: [jira.webresources:jira-setup, jira.webresources:_polyfills, com.atlassian.frontend.atlassian-frontend-runtime-plugin:core-js, com.atlassian.plugins.atlassian-plugins-webresource-plugin:context-path, com.atlassian.auiplugin:aui-core, com.atlassian.auiplugin:split_aui.splitchunk.2a34183e72]
2025-09-10 13:20:46,386+0800 http-nio-8080-exec-10 WARN anonymous 800x10x1 19bn0kh ********** /secure/SetupDatabase!default.jspa [webresource] cyclic plugin resource dependency has been detected with: com.atlassian.plugins.atlassian-plugins-webresource-plugin:context-path, stack trace: [jira.webresources:jira-setup, jira.webresources:_polyfills, com.atlassian.frontend.atlassian-frontend-runtime-plugin:core-js, com.atlassian.plugins.atlassian-plugins-webresource-plugin:context-path, com.atlassian.auiplugin:aui-core, com.atlassian.auiplugin:split_aui.core]
2025-09-10 13:20:46,386+0800 http-nio-8080-exec-10 WARN anonymous 800x10x1 19bn0kh ********** /secure/SetupDatabase!default.jspa [webresource] cyclic plugin resource dependency has been detected with: com.atlassian.plugins.atlassian-plugins-webresource-plugin:context-path, stack trace: [jira.webresources:jira-setup, jira.webresources:_polyfills, com.atlassian.frontend.atlassian-frontend-runtime-plugin:core-js, com.atlassian.plugins.atlassian-plugins-webresource-plugin:context-path, com.atlassian.auiplugin:aui-core, com.atlassian.auiplugin:common-runtime]
2025-09-10 13:20:46,387+0800 http-nio-8080-exec-10 WARN anonymous 800x10x1 19bn0kh ********** /secure/SetupDatabase!default.jspa [webresource] cyclic plugin resource dependency has been detected with: com.atlassian.plugins.atlassian-plugins-webresource-plugin:context-path, stack trace: [jira.webresources:jira-setup, jira.webresources:_polyfills, com.atlassian.frontend.atlassian-frontend-runtime-plugin:core-js, com.atlassian.plugins.atlassian-plugins-webresource-plugin:context-path, com.atlassian.auiplugin:aui-core]
2025-09-10 13:20:46,387+0800 http-nio-8080-exec-10 WARN anonymous 800x10x1 19bn0kh ********** /secure/SetupDatabase!default.jspa [webresource] cyclic plugin resource dependency has been detected with: com.atlassian.plugins.atlassian-plugins-webresource-plugin:context-path, stack trace: [jira.webresources:jira-setup, jira.webresources:_polyfills, com.atlassian.frontend.atlassian-frontend-runtime-plugin:core-js, com.atlassian.plugins.atlassian-plugins-webresource-plugin:context-path, com.atlassian.auiplugin:aui-core, com.atlassian.auiplugin:split_aui.splitchunk.0d131bcbf1]
2025-09-10 13:20:46,387+0800 http-nio-8080-exec-10 WARN anonymous 800x10x1 19bn0kh ********** /secure/SetupDatabase!default.jspa [webresource] cyclic plugin resource dependency has been detected with: com.atlassian.plugins.atlassian-plugins-webresource-plugin:context-path, stack trace: [jira.webresources:jira-setup, jira.webresources:_polyfills, com.atlassian.frontend.atlassian-frontend-runtime-plugin:core-js, com.atlassian.plugins.atlassian-plugins-webresource-plugin:context-path, com.atlassian.auiplugin:aui-core, com.atlassian.auiplugin:split_aui.splitchunk.444efc83be]
2025-09-10 13:20:46,388+0800 http-nio-8080-exec-10 WARN anonymous 800x10x1 19bn0kh ********** /secure/SetupDatabase!default.jspa [webresource] cyclic plugin resource dependency has been detected with: com.atlassian.plugins.atlassian-plugins-webresource-plugin:context-path, stack trace: [jira.webresources:jira-setup, jira.webresources:_polyfills, com.atlassian.frontend.atlassian-frontend-runtime-plugin:core-js, com.atlassian.plugins.atlassian-plugins-webresource-plugin:context-path, com.atlassian.auiplugin:aui-core, com.atlassian.auiplugin:split_aui.splitchunk.78b1c5d416]
2025-09-10 13:20:46,388+0800 http-nio-8080-exec-10 WARN anonymous 800x10x1 19bn0kh ********** /secure/SetupDatabase!default.jspa [webresource] cyclic plugin resource dependency has been detected with: com.atlassian.plugins.atlassian-plugins-webresource-plugin:context-path, stack trace: [jira.webresources:jira-setup, jira.webresources:_polyfills, com.atlassian.frontend.atlassian-frontend-runtime-plugin:core-js, com.atlassian.plugins.atlassian-plugins-webresource-plugin:context-path, com.atlassian.auiplugin:aui-core, com.atlassian.auiplugin:split_aui.splitchunk.9bbf750f52]
2025-09-10 13:20:46,388+0800 http-nio-8080-exec-10 WARN anonymous 800x10x1 19bn0kh ********** /secure/SetupDatabase!default.jspa [webresource] cyclic plugin resource dependency has been detected with: com.atlassian.plugins.atlassian-plugins-webresource-plugin:context-path, stack trace: [jira.webresources:jira-setup, jira.webresources:_polyfills, com.atlassian.frontend.atlassian-frontend-runtime-plugin:core-js, com.atlassian.plugins.atlassian-plugins-webresource-plugin:context-path, com.atlassian.auiplugin:aui-core, com.atlassian.auiplugin:split_aui.splitchunk.479fe6ee76]
2025-09-10 13:20:46,388+0800 http-nio-8080-exec-10 WARN anonymous 800x10x1 19bn0kh ********** /secure/SetupDatabase!default.jspa [webresource] cyclic plugin resource dependency has been detected with: com.atlassian.plugins.atlassian-plugins-webresource-plugin:context-path, stack trace: [jira.webresources:jira-setup, jira.webresources:_polyfills, com.atlassian.frontend.atlassian-frontend-runtime-plugin:core-js, com.atlassian.plugins.atlassian-plugins-webresource-plugin:context-path, com.atlassian.auiplugin:aui-core, com.atlassian.auiplugin:split_aui.splitchunk.e7c127e2cc]
2025-09-10 13:20:46,389+0800 http-nio-8080-exec-10 WARN anonymous 800x10x1 19bn0kh ********** /secure/SetupDatabase!default.jspa [webresource] cyclic plugin resource dependency has been detected with: com.atlassian.plugins.atlassian-plugins-webresource-plugin:context-path, stack trace: [jira.webresources:jira-setup, jira.webresources:_polyfills, com.atlassian.frontend.atlassian-frontend-runtime-plugin:core-js, com.atlassian.plugins.atlassian-plugins-webresource-plugin:context-path, com.atlassian.auiplugin:aui-core, com.atlassian.auiplugin:split_aui.splitchunk.8771ceac91]
2025-09-10 13:20:46,390+0800 http-nio-8080-exec-10 WARN anonymous 800x10x1 19bn0kh ********** /secure/SetupDatabase!default.jspa [webresource] cyclic plugin resource dependency has been detected with: com.atlassian.plugins.atlassian-plugins-webresource-plugin:context-path, stack trace: [jira.webresources:jira-setup, jira.webresources:_polyfills, com.atlassian.frontend.atlassian-frontend-runtime-plugin:core-js, com.atlassian.plugins.atlassian-plugins-webresource-plugin:context-path, com.atlassian.auiplugin:aui-core, com.atlassian.auiplugin:split_aui.splitchunk.0fc208f3fe]
2025-09-10 13:20:46,390+0800 http-nio-8080-exec-10 WARN anonymous 800x10x1 19bn0kh ********** /secure/SetupDatabase!default.jspa [webresource] cyclic plugin resource dependency has been detected with: com.atlassian.plugins.atlassian-plugins-webresource-plugin:context-path, stack trace: [jira.webresources:jira-setup, jira.webresources:_polyfills, com.atlassian.frontend.atlassian-frontend-runtime-plugin:core-js, com.atlassian.plugins.atlassian-plugins-webresource-plugin:context-path, com.atlassian.auiplugin:aui-core, com.atlassian.auiplugin:split_aui.splitchunk.7873ba9060]
2025-09-10 13:20:46,390+0800 http-nio-8080-exec-10 WARN anonymous 800x10x1 19bn0kh ********** /secure/SetupDatabase!default.jspa [webresource] cyclic plugin resource dependency has been detected with: com.atlassian.plugins.atlassian-plugins-webresource-plugin:context-path, stack trace: [jira.webresources:jira-setup, jira.webresources:_polyfills, com.atlassian.frontend.atlassian-frontend-runtime-plugin:core-js, com.atlassian.plugins.atlassian-plugins-webresource-plugin:context-path, com.atlassian.auiplugin:aui-core, com.atlassian.auiplugin:split_aui.splitchunk.2a34183e72]
2025-09-10 13:20:46,390+0800 http-nio-8080-exec-10 WARN anonymous 800x10x1 19bn0kh ********** /secure/SetupDatabase!default.jspa [webresource] cyclic plugin resource dependency has been detected with: com.atlassian.plugins.atlassian-plugins-webresource-plugin:context-path, stack trace: [jira.webresources:jira-setup, jira.webresources:_polyfills, com.atlassian.frontend.atlassian-frontend-runtime-plugin:core-js, com.atlassian.plugins.atlassian-plugins-webresource-plugin:context-path, com.atlassian.auiplugin:aui-core, com.atlassian.auiplugin:split_aui.core]
2025-09-10 13:24:34,308+0800 plugin-transaction-0 INFO      [c.a.jira.plugin.PluginTransactionListener] [plugin-transaction] numberStartEvents:4, numberEndEvents:4, numberSendEvents:39, numberEventsInTransactions:1916, numberOfPluginEnableEvents:38
2025-09-10 13:29:34,293+0800 plugin-transaction-0 INFO      [c.a.jira.plugin.PluginTransactionListener] [plugin-transaction] numberStartEvents:4, numberEndEvents:4, numberSendEvents:39, numberEventsInTransactions:1916, numberOfPluginEnableEvents:38
2025-09-10 13:34:34,276+0800 plugin-transaction-0 INFO      [c.a.jira.plugin.PluginTransactionListener] [plugin-transaction] numberStartEvents:4, numberEndEvents:4, numberSendEvents:39, numberEventsInTransactions:1916, numberOfPluginEnableEvents:38
2025-09-10 13:39:34,259+0800 plugin-transaction-0 INFO      [c.a.jira.plugin.PluginTransactionListener] [plugin-transaction] numberStartEvents:4, numberEndEvents:4, numberSendEvents:39, numberEventsInTransactions:1916, numberOfPluginEnableEvents:38
2025-09-10 13:44:34,243+0800 plugin-transaction-0 INFO      [c.a.jira.plugin.PluginTransactionListener] [plugin-transaction] numberStartEvents:4, numberEndEvents:4, numberSendEvents:39, numberEventsInTransactions:1916, numberOfPluginEnableEvents:38
2025-09-10 13:49:34,227+0800 plugin-transaction-0 INFO      [c.a.jira.plugin.PluginTransactionListener] [plugin-transaction] numberStartEvents:4, numberEndEvents:4, numberSendEvents:39, numberEventsInTransactions:1916, numberOfPluginEnableEvents:38
