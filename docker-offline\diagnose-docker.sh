#!/bin/bash

# =======================================================
# Docker 问题诊断脚本
# 用于诊断Docker安装和启动问题
# =======================================================

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

log_warn() {
    echo -e "${YELLOW}[WARN]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

log_step() {
    echo -e "${BLUE}[STEP]${NC} $1"
}

echo "Docker 问题诊断脚本"
echo "========================================"

# 1. 检查系统信息
log_step "检查系统信息..."
echo "系统版本:"
cat /etc/redhat-release 2>/dev/null || echo "无法获取系统版本"
echo "内核版本:"
uname -r
echo "架构:"
uname -m

# 2. 检查Docker安装状态
log_step "检查Docker安装状态..."
if command -v docker >/dev/null 2>&1; then
    log_info "✅ Docker命令已安装"
    docker --version 2>/dev/null || log_error "Docker版本获取失败"
else
    log_error "❌ Docker命令未找到"
fi

# 3. 检查Docker包安装状态
log_step "检查Docker包安装状态..."
echo "已安装的Docker相关包:"
rpm -qa | grep -E "(docker|containerd)" | sort || echo "未找到Docker相关包"

# 4. 检查systemd服务状态
log_step "检查systemd服务状态..."
echo "Docker服务状态:"
systemctl status docker --no-pager 2>/dev/null || echo "Docker服务未找到或状态异常"

echo "Containerd服务状态:"
systemctl status containerd --no-pager 2>/dev/null || echo "Containerd服务未找到或状态异常"

# 5. 检查服务文件
log_step "检查服务文件..."
if [ -f /usr/lib/systemd/system/docker.service ]; then
    log_info "✅ Docker服务文件存在"
    echo "服务文件权限:"
    ls -la /usr/lib/systemd/system/docker.service
else
    log_error "❌ Docker服务文件不存在"
fi

if [ -f /usr/lib/systemd/system/docker.socket ]; then
    log_info "✅ Docker socket文件存在"
else
    log_warn "⚠️ Docker socket文件不存在"
fi

# 6. 检查Docker进程
log_step "检查Docker进程..."
echo "Docker相关进程:"
ps aux | grep -E "(docker|containerd)" | grep -v grep || echo "未找到Docker相关进程"

# 7. 检查Docker socket
log_step "检查Docker socket..."
if [ -S /var/run/docker.sock ]; then
    log_info "✅ Docker socket存在"
    ls -la /var/run/docker.sock
else
    log_error "❌ Docker socket不存在"
fi

# 8. 检查Docker目录
log_step "检查Docker目录..."
if [ -d /var/lib/docker ]; then
    log_info "✅ Docker数据目录存在"
    echo "目录权限:"
    ls -ld /var/lib/docker
    echo "目录大小:"
    du -sh /var/lib/docker 2>/dev/null || echo "无法获取目录大小"
else
    log_warn "⚠️ Docker数据目录不存在"
fi

# 9. 检查系统资源
log_step "检查系统资源..."
echo "内存使用:"
free -h
echo "磁盘使用:"
df -h /var/lib/docker 2>/dev/null || df -h /

# 10. 检查网络配置
log_step "检查网络配置..."
echo "网络接口:"
ip addr show | grep -E "(docker|br-)" || echo "未找到Docker网络接口"

# 11. 检查SELinux状态
log_step "检查SELinux状态..."
if command -v getenforce >/dev/null 2>&1; then
    echo "SELinux状态: $(getenforce)"
else
    echo "SELinux未安装或不可用"
fi

# 12. 检查防火墙状态
log_step "检查防火墙状态..."
if systemctl is-active --quiet firewalld 2>/dev/null; then
    log_info "防火墙正在运行"
    echo "防火墙规则:"
    firewall-cmd --list-all 2>/dev/null || echo "无法获取防火墙规则"
else
    log_info "防火墙未运行"
fi

# 13. 检查日志
log_step "检查系统日志..."
echo "Docker服务日志（最近20行）:"
journalctl -u docker --no-pager -n 20 2>/dev/null || echo "无法获取Docker日志"

echo "系统启动日志中的Docker相关信息:"
journalctl -b --no-pager | grep -i docker | tail -10 2>/dev/null || echo "未找到Docker相关日志"

# 14. 检查依赖
log_step "检查依赖包..."
echo "关键依赖包状态:"
for pkg in libseccomp libcgroup container-selinux; do
    if rpm -qa | grep -q "$pkg"; then
        echo "✅ $pkg: $(rpm -qa | grep "$pkg")"
    else
        echo "❌ $pkg: 未安装"
    fi
done

# 15. 提供修复建议
log_step "修复建议..."
echo ""
echo "========================================"
echo "  修复建议"
echo "========================================"
echo ""

if ! command -v docker >/dev/null 2>&1; then
    echo "1. Docker未安装，请运行安装脚本:"
    echo "   ./install-docker-final.sh"
elif ! systemctl is-active --quiet docker 2>/dev/null; then
    echo "1. 尝试启动Docker服务:"
    echo "   systemctl start docker"
    echo ""
    echo "2. 如果启动失败，尝试重新加载systemd:"
    echo "   systemctl daemon-reload"
    echo "   systemctl start docker"
    echo ""
    echo "3. 检查服务文件是否存在:"
    echo "   ls -la /usr/lib/systemd/system/docker.service"
    echo ""
    echo "4. 如果服务文件不存在，重新运行安装脚本"
else
    echo "1. Docker服务正在运行，尝试验证功能:"
    echo "   docker info"
    echo "   docker run hello-world"
fi

echo ""
echo "如果问题仍然存在，请："
echo "1. 重新运行安装脚本: ./install-docker-final.sh"
echo "2. 检查系统日志: journalctl -u docker -f"
echo "3. 手动启动dockerd: /usr/bin/dockerd"
echo ""
