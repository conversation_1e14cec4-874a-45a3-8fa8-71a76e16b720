#!/bin/bash

# =======================================================
# Docker CE CentOS 7 生产环境离线安装脚本
# 版本: 1.0
# 支持: CentOS 7.x
# =======================================================

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

log_warn() {
    echo -e "${YELLOW}[WARN]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

log_step() {
    echo -e "${BLUE}[STEP]${NC} $1"
}

# 检查root权限
check_root() {
    if [[ $EUID -ne 0 ]]; then
        log_error "此脚本需要root权限运行"
        log_info "请使用: sudo $0"
        exit 1
    fi
}

# 检查系统版本
check_system() {
    if [ ! -f /etc/redhat-release ]; then
        log_error "不支持的系统，此脚本仅适用于CentOS 7"
        exit 1
    fi
    
    if ! grep -q "CentOS Linux release 7" /etc/redhat-release; then
        log_error "不支持的CentOS版本，此脚本仅适用于CentOS 7"
        exit 1
    fi
    
    log_info "系统检查通过: $(cat /etc/redhat-release)"
}

# 检查安装包
check_packages() {
    local packages=(
        "libcgroup-0.41-21.el7.x86_64.rpm"
        "container-selinux-2.119.2-1.911c772.el7_8.noarch.rpm"
        "containerd.io-1.6.9-3.1.el7.x86_64.rpm"
        "docker-ce-cli-26.1.4-1.el7.x86_64.rpm"
        "docker-ce-26.1.4-1.el7.x86_64.rpm"
        "docker-compose-plugin-2.21.0-1.el7.x86_64.rpm"
    )
    
    log_step "检查安装包完整性..."
    for package in "${packages[@]}"; do
        if [ ! -f "$package" ]; then
            log_error "缺少安装包: $package"
            exit 1
        fi
        log_info "✓ $package"
    done
}

# 卸载旧版本Docker
remove_old_docker() {
    log_step "卸载旧版本Docker..."
    
    yum remove -y docker \
                  docker-client \
                  docker-client-latest \
                  docker-common \
                  docker-latest \
                  docker-latest-logrotate \
                  docker-logrotate \
                  docker-engine \
                  docker-ce \
                  docker-ce-cli \
                  containerd.io 2>/dev/null || true
    
    log_info "旧版本Docker卸载完成"
}

# 安装依赖包
install_dependencies() {
    log_step "安装系统依赖包..."
    
    # 基础依赖
    rpm -ivh libcgroup-0.41-21.el7.x86_64.rpm || {
        log_warn "libcgroup可能已安装，跳过"
    }
    
    # SELinux依赖
    rpm -ivh container-selinux-2.119.2-1.911c772.el7_8.noarch.rpm || {
        log_warn "container-selinux可能已安装，跳过"
    }
    
    log_info "依赖包安装完成"
}

# 安装Docker CE
install_docker() {
    log_step "安装Docker CE..."
    
    # 安装containerd
    rpm -ivh containerd.io-1.6.9-3.1.el7.x86_64.rpm
    
    # 安装Docker CLI
    rpm -ivh docker-ce-cli-26.1.4-1.el7.x86_64.rpm
    
    # 安装Docker CE
    rpm -ivh docker-ce-26.1.4-1.el7.x86_64.rpm
    
    # 安装Docker Compose插件
    rpm -ivh docker-compose-plugin-2.21.0-1.el7.x86_64.rpm
    
    log_info "Docker CE安装完成"
}

# 配置Docker服务
configure_docker() {
    log_step "配置Docker服务..."
    
    # 创建docker组
    groupadd docker 2>/dev/null || true
    
    # 创建Docker配置目录
    mkdir -p /etc/docker
    
    # 生产环境Docker daemon配置
    cat > /etc/docker/daemon.json << EOF
{
    "log-driver": "json-file",
    "log-opts": {
        "max-size": "100m",
        "max-file": "3"
    },
    "storage-driver": "overlay2",
    "storage-opts": [
        "overlay2.override_kernel_check=true"
    ],
    "live-restore": true,
    "userland-proxy": false,
    "no-new-privileges": true,
    "default-ulimits": {
        "nofile": {
            "name": "nofile",
            "hard": 64000,
            "soft": 64000
        }
    }
}
EOF

    # 启用并启动Docker服务
    systemctl enable docker
    systemctl start docker
    
    # 验证Docker状态
    systemctl status docker --no-pager
    
    log_info "Docker服务配置完成"
}

# 验证安装
verify_installation() {
    log_step "验证安装..."
    
    # 检查Docker版本
    docker --version
    docker compose version
    
    # 检查Docker服务状态
    if systemctl is-active --quiet docker; then
        log_info "✓ Docker服务运行正常"
    else
        log_error "✗ Docker服务未运行"
        exit 1
    fi
    
    # 运行测试容器
    log_info "运行测试容器..."
    docker run --rm hello-world
    
    log_info "Docker安装验证成功！"
}

# 优化系统设置
optimize_system() {
    log_step "优化系统设置..."
    
    # 优化内核参数
    cat >> /etc/sysctl.conf << 'EOF'

# Docker优化参数
net.bridge.bridge-nf-call-ip6tables = 1
net.bridge.bridge-nf-call-iptables = 1
net.ipv4.ip_forward = 1
vm.max_map_count = 262144
EOF

    sysctl -p
    
    # 设置防火墙（生产环境）
    if systemctl is-active --quiet firewalld; then
        log_info "配置防火墙规则..."
        firewall-cmd --permanent --zone=public --add-port=8080/tcp  # JIRA
        firewall-cmd --permanent --zone=public --add-port=3307/tcp  # MySQL (可选)
        firewall-cmd --reload
    fi
    
    log_info "系统优化完成"
}

# 安装后指导
post_install_guide() {
    echo
    log_info "=========================================="
    log_info "Docker CE 安装完成！"
    log_info "=========================================="
    echo
    log_info "版本信息："
    docker --version
    docker compose version
    echo
    log_info "下一步操作："
    log_info "1. 将普通用户添加到docker组："
    log_info "   sudo usermod -aG docker \$USER"
    log_info "2. 重新登录以使组变更生效"
    log_info "3. 部署JIRA应用："
    log_info "   cd /path/to/jira-project"
    log_info "   docker compose up -d"
    echo
    log_info "管理命令："
    log_info "- 启动Docker: sudo systemctl start docker"
    log_info "- 停止Docker: sudo systemctl stop docker"
    log_info "- 重启Docker: sudo systemctl restart docker"
    log_info "- 查看状态: sudo systemctl status docker"
    echo
}

# 主函数
main() {
    echo "========================================"
    echo "Docker CE CentOS 7 生产环境离线安装"
    echo "========================================"
    echo
    
    check_root
    check_system
    check_packages
    
    read -p "确认开始安装Docker CE? (y/N): " -n 1 -r
    echo
    if [[ ! $REPLY =~ ^[Yy]$ ]]; then
        log_info "安装已取消"
        exit 0
    fi
    
    remove_old_docker
    install_dependencies
    install_docker
    configure_docker
    optimize_system
    verify_installation
    post_install_guide
    
    log_info "安装完成！"
}

# 错误处理
trap 'log_error "安装过程中发生错误，请检查日志"; exit 1' ERR

# 执行主函数
main "$@" 