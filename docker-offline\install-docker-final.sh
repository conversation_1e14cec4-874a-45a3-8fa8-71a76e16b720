#!/bin/bash

# =======================================================
# Docker CE CentOS 7 完整离线安装脚本 - 最终版本
# 版本: 3.0
# 支持: CentOS 7.x
# 功能: 自动下载依赖包并安装Docker CE
# =======================================================

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

log_warn() {
    echo -e "${YELLOW}[WARN]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

log_step() {
    echo -e "${BLUE}[STEP]${NC} $1"
}

# 获取脚本目录
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"

echo "Docker CE CentOS 7 离线安装脚本"
echo "========================================"

# 检查root权限
check_root() {
    if [[ $EUID -ne 0 ]]; then
        log_error "此脚本需要root权限运行"
        log_info "请使用: sudo $0"
        exit 1
    fi
}

# 检查系统版本
check_system() {
    if [ ! -f /etc/redhat-release ]; then
        log_error "不支持的系统，此脚本仅适用于CentOS 7"
        exit 1
    fi
    
    local version=$(cat /etc/redhat-release)
    if [[ ! "$version" =~ "CentOS Linux release 7" ]] && [[ ! "$version" =~ "CentOS Linux 7" ]]; then
        log_error "不支持的CentOS版本: $version"
        log_info "此脚本仅适用于CentOS 7.x"
        exit 1
    fi
    
    log_info "系统检查通过: $version"
}



# 检查所需的RPM包是否存在
check_required_packages() {
    log_step "检查本地RPM包..."
    
    cd "$SCRIPT_DIR"
    
    local required_packages=(
        "libcgroup-0.41-21.el7.x86_64.rpm"
        "libseccomp-2.3.1-4.el7.x86_64.rpm"
        "setools-libs-3.3.8-4.el7.x86_64.rpm"
        "python-IPy-0.75-6.el7.noarch.rpm"
        "libselinux-python-2.5-15.el7.x86_64.rpm"
        "libsemanage-python-2.5-14.el7.x86_64.rpm"
        "audit-libs-python-2.8.5-4.el7.x86_64.rpm"
        "checkpolicy-2.5-8.el7.x86_64.rpm"
        "policycoreutils-2.5-34.el7.x86_64.rpm"
        "policycoreutils-python-2.5-34.el7.x86_64.rpm"
        "container-selinux-2.119.2-1.911c772.el7_8.noarch.rpm"
        "containerd.io-1.6.9-3.1.el7.x86_64.rpm"
        "docker-ce-cli-26.1.4-1.el7.x86_64.rpm"
        "docker-ce-26.1.4-1.el7.x86_64.rpm"
        "docker-compose-plugin-2.21.0-1.el7.x86_64.rpm"
        "docker-buildx-plugin-0.11.2-1.el7.x86_64.rpm"
    )
    
    local available_packages=0
    local missing_packages=()
    
    for package in "${required_packages[@]}"; do
        if [ -f "$package" ]; then
            local size=$(stat -c%s "$package" 2>/dev/null || echo "0")
            log_info "✅ $package ($(echo $size | awk '{print int($1/1024)}')KB)"
            ((available_packages++))
        else
            log_warn "❌ $package (缺失)"
            missing_packages+=("$package")
        fi
    done
    
    log_info "可用包: ${available_packages}/${#required_packages[@]}"
    
    if [ ${#missing_packages[@]} -gt 0 ]; then
        log_warn "缺失的包:"
        for pkg in "${missing_packages[@]}"; do
            log_warn "  - $pkg"
        done
        log_warn "将跳过缺失的包继续安装"
    fi
    
    if [ $available_packages -lt 10 ]; then
        log_error "可用包太少（少于10个），无法继续安装"
        log_error "请确保所有必需的RPM包都在当前目录中"
        return 1
    fi
    
    return 0
}

# 卸载旧版本Docker
remove_old_docker() {
    log_step "检查并移除旧版本Docker..."
    
    local old_packages=(
        "docker"
        "docker-client"
        "docker-client-latest"
        "docker-common"
        "docker-latest"
        "docker-latest-logrotate"
        "docker-logrotate"
        "docker-engine"
        "podman"
        "runc"
    )
    
    for package in "${old_packages[@]}"; do
        if rpm -qa | grep -q "^$package"; then
            log_warn "移除旧版本: $package"
            yum remove -y "$package" 2>/dev/null || true
        fi
    done
    
    log_info "旧版本清理完成"
}

# 安装单个RPM包
install_rpm() {
    local package="$1"
    local required="$2"  # true/false
    
    if [ ! -f "$package" ]; then
        if [ "$required" = "true" ]; then
            log_error "❌ 缺失必需包: $package"
            return 1
        else
            log_warn "⏭️ 可选包不存在，跳过: $package"
            return 0
        fi
    fi
    

    
    # 检查包是否已安装
    local pkg_name=$(rpm -qp "$package" --queryformat="%{NAME}" 2>/dev/null || echo "")
    if [ -n "$pkg_name" ] && rpm -qa | grep -q "^$pkg_name-"; then
        log_info "⏭️ 包 $pkg_name 已安装，跳过"
        return 0
    fi
    
    log_info "📦 安装: $package"
    
    # 尝试安装包 - 多重策略确保成功
    local install_success=false
    
    # 策略1: 正常安装（跳过签名检查）
    if rpm -ivh "$package" --nosignature --nodigest 2>/dev/null; then
        log_info "✅ $package 安装成功"
        install_success=true
    # 策略2: 强制安装（忽略依赖）
    elif rpm -ivh "$package" --force --nodeps --nosignature --nodigest 2>/dev/null; then
        log_info "✅ $package 强制安装成功"
        install_success=true
    # 策略3: 替换安装（如果已有旧版本）
    elif rpm -Uvh "$package" --force --nodeps --nosignature --nodigest 2>/dev/null; then
        log_info "✅ $package 替换安装成功"
        install_success=true
    # 策略4: 忽略所有检查的安装
    elif rpm -ivh "$package" --force --nodeps --nosignature --nodigest --noscripts 2>/dev/null; then
        log_info "✅ $package 完全强制安装成功"
        install_success=true
    fi
    
    if [ "$install_success" = "false" ]; then
        if [ "$required" = "true" ]; then
            log_error "❌ $package 所有安装策略都失败"
            # 显示详细错误信息
            log_error "最后一次安装尝试的错误信息:"
            rpm -ivh "$package" --force --nodeps --nosignature --nodigest
            return 1
        else
            log_warn "⚠️ $package 安装失败，但为可选包，继续..."
            return 0
        fi
    fi
    
    return 0
}

# 创建Docker systemd服务文件
create_docker_service() {
    log_step "检查并创建Docker systemd服务文件..."

    local service_file="/usr/lib/systemd/system/docker.service"
    local service_dir="/usr/lib/systemd/system"

    # 确保systemd目录存在
    mkdir -p "$service_dir"

    # 检查服务文件是否存在且有效
    if [ -f "$service_file" ]; then
        log_info "Docker服务文件已存在: $service_file"
        # 验证服务文件内容
        if grep -q "ExecStart.*dockerd" "$service_file"; then
            log_info "✅ Docker服务文件内容有效"
            return 0
        else
            log_warn "Docker服务文件内容无效，将重新创建"
        fi
    fi

    log_info "创建Docker systemd服务文件..."

    cat > "$service_file" << 'EOF'
[Unit]
Description=Docker Application Container Engine
Documentation=https://docs.docker.com
After=network-online.target firewalld.service containerd.service
Wants=network-online.target
Requires=docker.socket containerd.service

[Service]
Type=notify
# the default is not to use systemd for cgroups because the delegate issues still
# exists and systemd currently does not support the cgroup feature set required
# for containers run by docker
ExecStart=/usr/bin/dockerd -H fd:// --containerd=/run/containerd/containerd.sock
ExecReload=/bin/kill -s HUP $MAINPID
TimeoutSec=0
RestartSec=2
Restart=always

# Note that StartLimit* options were moved from "Service" to "Unit" in systemd 229.
# Both the old, and new location are accepted by systemd 229 and up, so using the old location
# to make them work for either version of systemd.
StartLimitBurst=3

# Note that StartLimitInterval was renamed to StartLimitIntervalSec in systemd 230.
# Both the old, and new name are accepted by systemd 230 and up, so using the old name to make
# this option work for either version of systemd.
StartLimitInterval=60s

# Having non-zero Limit*s causes performance problems due to accounting overhead
# in the kernel. We recommend using cgroups to do container-local accounting.
LimitNOFILE=infinity
LimitNPROC=infinity
LimitCORE=infinity

# Comment TasksMax if your systemd version does not supports it.
# Only systemd 226 and above support this option.
TasksMax=infinity

# set delegate yes so that systemd does not reset the cgroups of docker containers
Delegate=yes

# kill only the docker process, not all processes in the cgroup
KillMode=process

[Install]
WantedBy=multi-user.target
EOF

    # 创建docker.socket文件
    local socket_file="/usr/lib/systemd/system/docker.socket"
    cat > "$socket_file" << 'EOF'
[Unit]
Description=Docker Socket for the API
PartOf=docker.service

[Socket]
ListenStream=/var/run/docker.sock
SocketMode=0660
SocketUser=root
SocketGroup=docker

[Install]
WantedBy=sockets.target
EOF

    # 设置正确的权限
    chmod 644 "$service_file"
    chmod 644 "$socket_file"

    log_info "✅ Docker systemd服务文件创建完成"
    return 0
}

# 配置Docker daemon
configure_docker_daemon() {
    log_step "配置Docker daemon..."

    # 创建Docker配置目录
    mkdir -p /etc/docker

    # 创建Docker daemon配置文件
    cat > /etc/docker/daemon.json << 'EOF'
{
    "storage-driver": "overlay2",
    "log-driver": "json-file",
    "log-opts": {
        "max-size": "100m",
        "max-file": "3"
    },
    "live-restore": true,
    "userland-proxy": false,
    "experimental": false,
    "icc": true,
    "debug": false,
    "hosts": ["fd://", "unix:///var/run/docker.sock"],
    "containerd": "/run/containerd/containerd.sock"
}
EOF

    # 创建Docker数据目录
    mkdir -p /var/lib/docker

    # 设置正确的权限
    chmod 755 /etc/docker
    chmod 644 /etc/docker/daemon.json
    chmod 755 /var/lib/docker

    log_info "✅ Docker daemon配置完成"
}

# 安装Docker及其依赖
install_docker() {
    log_step "安装Docker CE及其依赖..."

    cd "$SCRIPT_DIR"

    # 按正确顺序安装 - 标记每个包是否为必需
    local install_sequence=(
        # 格式: "包名:是否必需(true/false)"

        # 基础系统库
        "libcgroup-0.41-21.el7.x86_64.rpm:true"
        "libseccomp-2.3.1-4.el7.x86_64.rpm:true"
        "setools-libs-3.3.8-4.el7.x86_64.rpm:true"
        "python-IPy-0.75-6.el7.noarch.rpm:true"

        # SELinux相关依赖
        "libselinux-python-2.5-15.el7.x86_64.rpm:true"
        "libsemanage-python-2.5-14.el7.x86_64.rpm:true"
        "audit-libs-python-2.8.5-4.el7.x86_64.rpm:true"
        "checkpolicy-2.5-8.el7.x86_64.rpm:true"

        # 策略管理工具
        "policycoreutils-2.5-34.el7.x86_64.rpm:true"
        "policycoreutils-python-2.5-34.el7.x86_64.rpm:true"

        # 容器相关
        "container-selinux-2.119.2-1.911c772.el7_8.noarch.rpm:true"
        "containerd.io-1.6.9-3.1.el7.x86_64.rpm:true"

        # Docker主程序
        "docker-ce-cli-26.1.4-1.el7.x86_64.rpm:true"
        "docker-ce-26.1.4-1.el7.x86_64.rpm:true"

        # Docker插件
        "docker-compose-plugin-2.21.0-1.el7.x86_64.rpm:true"
        "docker-buildx-plugin-0.11.2-1.el7.x86_64.rpm:true"
    )

    local failed_packages=()

    for item in "${install_sequence[@]}"; do
        local package="${item%:*}"
        local required="${item#*:}"

        if ! install_rpm "$package" "$required"; then
            if [ "$required" = "true" ]; then
                failed_packages+=("$package")
            fi
        fi
    done

    if [ ${#failed_packages[@]} -gt 0 ]; then
        log_error "以下必需包安装失败:"
        for pkg in "${failed_packages[@]}"; do
            log_error "  - $pkg"
        done
        return 1
    fi

    log_info "Docker CE安装完成"
    return 0
}

# 启动Docker服务
start_docker_service() {
    log_step "启动Docker服务..."

    # 创建docker用户组
    groupadd docker 2>/dev/null || true

    # 重新加载systemd配置
    log_info "重新加载systemd配置..."
    systemctl daemon-reload

    # 检查服务文件是否被正确识别
    if ! systemctl list-unit-files | grep -q "docker.service"; then
        log_error "systemd无法识别docker.service，尝试手动注册..."
        systemctl daemon-reload
        sleep 2

        if ! systemctl list-unit-files | grep -q "docker.service"; then
            log_error "systemd仍无法识别docker.service"
            return 1
        fi
    fi

    log_info "✅ systemd已识别docker.service"

    # 启用Docker服务
    log_info "启用Docker服务..."
    if ! systemctl enable docker; then
        log_error "启用Docker服务失败"
        return 1
    fi

    # 启用Docker socket
    systemctl enable docker.socket 2>/dev/null || true

    # 启动containerd服务（如果存在）
    if systemctl list-unit-files | grep -q "containerd.service"; then
        log_info "启动containerd服务..."
        systemctl start containerd 2>/dev/null || true
    fi

    # 启动Docker服务
    log_info "启动Docker服务..."
    if systemctl start docker; then
        log_info "✅ Docker服务启动成功"
    else
        log_error "Docker服务启动失败，尝试诊断..."

        # 显示详细错误信息
        log_error "systemctl状态:"
        systemctl status docker --no-pager || true

        log_error "journalctl日志:"
        journalctl -u docker --no-pager -n 20 || true

        # 尝试手动启动dockerd
        log_info "尝试手动启动dockerd..."
        if /usr/bin/dockerd --version >/dev/null 2>&1; then
            log_info "dockerd二进制文件正常"
            # 尝试后台启动dockerd
            nohup /usr/bin/dockerd > /var/log/docker.log 2>&1 &
            sleep 5

            if pgrep dockerd >/dev/null; then
                log_info "✅ dockerd手动启动成功"
                return 0
            else
                log_error "dockerd手动启动也失败"
                return 1
            fi
        else
            log_error "dockerd二进制文件损坏或缺失"
            return 1
        fi
    fi

    return 0
}



# 验证安装
verify_installation() {
    log_step "验证Docker安装..."

    # 检查Docker命令
    if ! command -v docker >/dev/null 2>&1; then
        log_error "❌ Docker命令未找到"
        return 1
    fi

    log_info "✅ Docker命令已安装"

    # 等待Docker服务完全启动
    log_info "等待Docker服务启动..."
    local wait_count=0
    local max_wait=30

    while [ $wait_count -lt $max_wait ]; do
        if systemctl is-active --quiet docker 2>/dev/null; then
            log_info "✅ Docker服务已激活"
            break
        elif pgrep dockerd >/dev/null 2>&1; then
            log_info "✅ Docker daemon进程运行中"
            break
        fi

        sleep 2
        wait_count=$((wait_count + 1))

        if [ $((wait_count % 5)) -eq 0 ]; then
            log_info "等待Docker启动... ($wait_count/$max_wait)"
        fi
    done

    if [ $wait_count -ge $max_wait ]; then
        log_error "❌ Docker服务启动超时"

        # 显示诊断信息
        log_error "诊断信息:"
        echo "systemctl状态:"
        systemctl status docker --no-pager || true
        echo "Docker进程:"
        ps aux | grep docker || true
        echo "Docker socket:"
        ls -la /var/run/docker.sock 2>/dev/null || echo "Docker socket不存在"

        return 1
    fi

    # 验证Docker版本
    local docker_version=$(docker --version 2>/dev/null || echo "未知版本")
    log_info "Docker版本: $docker_version"

    # 验证Docker info命令
    log_info "验证Docker info命令..."
    local info_wait=0
    local max_info_wait=15

    while [ $info_wait -lt $max_info_wait ]; do
        if docker info >/dev/null 2>&1; then
            log_info "✅ Docker info命令验证成功"
            break
        fi

        sleep 2
        info_wait=$((info_wait + 1))

        if [ $((info_wait % 3)) -eq 0 ]; then
            log_info "等待Docker API就绪... ($info_wait/$max_info_wait)"
        fi
    done

    if [ $info_wait -ge $max_info_wait ]; then
        log_warn "⚠️ Docker info命令验证失败，但服务可能正在启动"
        log_warn "请稍后手动运行: docker info"
    fi

    # 检查Docker Compose插件
    if docker compose version >/dev/null 2>&1; then
        local compose_version=$(docker compose version 2>/dev/null || echo "未知版本")
        log_info "Docker Compose版本: $compose_version"
    else
        log_warn "⚠️ Docker Compose插件未安装或不可用"
    fi

    # 检查Docker socket权限
    if [ -S /var/run/docker.sock ]; then
        log_info "✅ Docker socket已创建"
        local socket_perms=$(ls -la /var/run/docker.sock 2>/dev/null || echo "无法获取权限信息")
        log_info "Socket权限: $socket_perms"
    else
        log_warn "⚠️ Docker socket未找到"
    fi

    # 最终验证
    if systemctl is-active --quiet docker 2>/dev/null || pgrep dockerd >/dev/null 2>&1; then
        log_info "✅ Docker安装和启动验证成功"
        return 0
    else
        log_error "❌ Docker验证失败"
        return 1
    fi
}



# 故障排除和修复
troubleshoot_and_fix() {
    log_step "执行故障排除和修复..."

    # 检查并修复常见问题

    # 1. 检查SELinux状态
    if command -v getenforce >/dev/null 2>&1; then
        local selinux_status=$(getenforce 2>/dev/null || echo "Unknown")
        log_info "SELinux状态: $selinux_status"

        if [ "$selinux_status" = "Enforcing" ]; then
            log_warn "SELinux处于强制模式，可能影响Docker运行"
            log_info "临时设置SELinux为宽松模式..."
            setenforce 0 2>/dev/null || true
        fi
    fi

    # 2. 检查防火墙状态
    if systemctl is-active --quiet firewalld 2>/dev/null; then
        log_info "防火墙正在运行，添加Docker规则..."
        firewall-cmd --permanent --zone=trusted --add-interface=docker0 2>/dev/null || true
        firewall-cmd --reload 2>/dev/null || true
    fi

    # 3. 检查cgroup挂载
    if ! mount | grep -q cgroup; then
        log_warn "cgroup未正确挂载，尝试挂载..."
        mount -t cgroup cgroup /sys/fs/cgroup 2>/dev/null || true
    fi

    # 4. 检查Docker目录权限
    if [ -d /var/lib/docker ]; then
        chmod 755 /var/lib/docker
        log_info "修复Docker数据目录权限"
    fi

    # 5. 清理可能的锁文件
    rm -f /var/run/docker.pid 2>/dev/null || true
    rm -f /var/run/docker.sock 2>/dev/null || true

    # 6. 重新加载systemd
    systemctl daemon-reload

    log_info "故障排除完成"
}

# 显示最终结果
show_result() {
    echo ""
    echo "========================================"
    echo "  Docker CE 离线安装完成"
    echo "========================================"
    echo ""

    # 显示Docker信息
    if command -v docker >/dev/null 2>&1; then
        local docker_version=$(docker --version 2>/dev/null || echo "Docker版本获取失败")
        echo "✅ $docker_version"
    fi

    if docker compose version >/dev/null 2>&1; then
        local compose_version=$(docker compose version 2>/dev/null || echo "Compose版本获取失败")
        echo "✅ $compose_version"
    fi

    echo ""
    echo "常用命令:"
    echo "  检查Docker状态: systemctl status docker"
    echo "  查看Docker信息: docker info"
    echo "  测试Docker运行: docker run hello-world"
    echo "  启动JIRA服务: cd .. && docker compose up -d"
    echo ""

    # 显示重要提示
    echo "重要提示:"
    echo "1. 如果Docker命令需要sudo，请将用户添加到docker组:"
    echo "   sudo usermod -aG docker \$USER"
    echo "   然后重新登录"
    echo ""
    echo "2. 如果遇到权限问题，请检查:"
    echo "   ls -la /var/run/docker.sock"
    echo ""
    echo "3. 查看Docker日志:"
    echo "   journalctl -u docker -f"
    echo ""
}

# 主函数
main() {
    check_root
    check_system

    # 检查本地RPM包
    if ! check_required_packages; then
        log_error "RPM包检查失败，无法继续安装"
        exit 1
    fi

    remove_old_docker

    # 安装Docker包
    if ! install_docker; then
        log_error "Docker安装失败"
        exit 1
    fi

    # 创建systemd服务文件
    if ! create_docker_service; then
        log_error "Docker服务文件创建失败"
        exit 1
    fi

    # 配置Docker daemon
    if ! configure_docker_daemon; then
        log_error "Docker daemon配置失败"
        exit 1
    fi

    # 启动Docker服务
    if ! start_docker_service; then
        log_error "Docker服务启动失败，尝试故障排除..."

        # 执行故障排除
        troubleshoot_and_fix

        # 再次尝试启动
        log_info "重新尝试启动Docker服务..."
        if start_docker_service; then
            log_info "✅ 故障排除后Docker启动成功"
        else
            log_error "❌ 故障排除后仍无法启动Docker"
            log_error "请检查系统日志: journalctl -u docker -n 50"
            log_error "或尝试手动启动: systemctl start docker"
            exit 1
        fi
    fi

    # 验证安装
    if verify_installation; then
        show_result
        exit 0
    else
        log_warn "Docker验证部分失败，但基本安装可能已完成"
        log_info "尝试基本验证..."

        if command -v docker >/dev/null 2>&1; then
            local docker_version=$(docker --version 2>/dev/null || echo "版本获取失败")
            log_info "Docker已安装: $docker_version"

            show_result
            log_warn "请手动验证Docker功能: docker info"
            exit 0
        else
            log_error "❌ Docker安装完全失败"
            exit 1
        fi
    fi
}

# 错误处理
trap 'log_error "脚本执行过程中出现错误，退出代码: $?"' ERR

# 运行主函数
main "$@" 