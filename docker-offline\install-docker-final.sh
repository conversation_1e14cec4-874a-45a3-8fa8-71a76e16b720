#!/bin/bash

# =======================================================
# Docker CE CentOS 7 完整离线安装脚本 - 最终版本
# 版本: 3.0
# 支持: CentOS 7.x
# 功能: 自动下载依赖包并安装Docker CE
# =======================================================

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

log_warn() {
    echo -e "${YELLOW}[WARN]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

log_step() {
    echo -e "${BLUE}[STEP]${NC} $1"
}

# 获取脚本目录
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"

echo "Docker CE CentOS 7 离线安装脚本"
echo "========================================"

# 检查root权限
check_root() {
    if [[ $EUID -ne 0 ]]; then
        log_error "此脚本需要root权限运行"
        log_info "请使用: sudo $0"
        exit 1
    fi
}

# 检查系统版本
check_system() {
    if [ ! -f /etc/redhat-release ]; then
        log_error "不支持的系统，此脚本仅适用于CentOS 7"
        exit 1
    fi
    
    local version=$(cat /etc/redhat-release)
    if [[ ! "$version" =~ "CentOS Linux release 7" ]] && [[ ! "$version" =~ "CentOS Linux 7" ]]; then
        log_error "不支持的CentOS版本: $version"
        log_info "此脚本仅适用于CentOS 7.x"
        exit 1
    fi
    
    log_info "系统检查通过: $version"
}



# 检查所需的RPM包是否存在
check_required_packages() {
    log_step "检查本地RPM包..."
    
    cd "$SCRIPT_DIR"
    
    local required_packages=(
        "libcgroup-0.41-21.el7.x86_64.rpm"
        "libseccomp-2.3.1-4.el7.x86_64.rpm"
        "setools-libs-3.3.8-4.el7.x86_64.rpm"
        "python-IPy-0.75-6.el7.noarch.rpm"
        "libselinux-python-2.5-15.el7.x86_64.rpm"
        "libsemanage-python-2.5-14.el7.x86_64.rpm"
        "audit-libs-python-2.8.5-4.el7.x86_64.rpm"
        "checkpolicy-2.5-8.el7.x86_64.rpm"
        "policycoreutils-2.5-34.el7.x86_64.rpm"
        "policycoreutils-python-2.5-34.el7.x86_64.rpm"
        "container-selinux-2.119.2-1.911c772.el7_8.noarch.rpm"
        "containerd.io-1.6.9-3.1.el7.x86_64.rpm"
        "docker-ce-cli-26.1.4-1.el7.x86_64.rpm"
        "docker-ce-26.1.4-1.el7.x86_64.rpm"
        "docker-compose-plugin-2.21.0-1.el7.x86_64.rpm"
        "docker-buildx-plugin-0.11.2-1.el7.x86_64.rpm"
    )
    
    local available_packages=0
    local missing_packages=()
    
    for package in "${required_packages[@]}"; do
        if [ -f "$package" ]; then
            local size=$(stat -c%s "$package" 2>/dev/null || echo "0")
            log_info "✅ $package ($(echo $size | awk '{print int($1/1024)}')KB)"
            ((available_packages++))
        else
            log_warn "❌ $package (缺失)"
            missing_packages+=("$package")
        fi
    done
    
    log_info "可用包: ${available_packages}/${#required_packages[@]}"
    
    if [ ${#missing_packages[@]} -gt 0 ]; then
        log_warn "缺失的包:"
        for pkg in "${missing_packages[@]}"; do
            log_warn "  - $pkg"
        done
        log_warn "将跳过缺失的包继续安装"
    fi
    
    if [ $available_packages -lt 10 ]; then
        log_error "可用包太少（少于10个），无法继续安装"
        log_error "请确保所有必需的RPM包都在当前目录中"
        return 1
    fi
    
    return 0
}

# 卸载旧版本Docker
remove_old_docker() {
    log_step "检查并移除旧版本Docker..."
    
    local old_packages=(
        "docker"
        "docker-client"
        "docker-client-latest"
        "docker-common"
        "docker-latest"
        "docker-latest-logrotate"
        "docker-logrotate"
        "docker-engine"
        "podman"
        "runc"
    )
    
    for package in "${old_packages[@]}"; do
        if rpm -qa | grep -q "^$package"; then
            log_warn "移除旧版本: $package"
            yum remove -y "$package" 2>/dev/null || true
        fi
    done
    
    log_info "旧版本清理完成"
}

# 安装单个RPM包
install_rpm() {
    local package="$1"
    local required="$2"  # true/false
    
    if [ ! -f "$package" ]; then
        if [ "$required" = "true" ]; then
            log_error "❌ 缺失必需包: $package"
            return 1
        else
            log_warn "⏭️ 可选包不存在，跳过: $package"
            return 0
        fi
    fi
    

    
    # 检查包是否已安装
    local pkg_name=$(rpm -qp "$package" --queryformat="%{NAME}" 2>/dev/null || echo "")
    if [ -n "$pkg_name" ] && rpm -qa | grep -q "^$pkg_name-"; then
        log_info "⏭️ 包 $pkg_name 已安装，跳过"
        return 0
    fi
    
    log_info "📦 安装: $package"
    
    # 尝试安装包 - 多重策略确保成功
    local install_success=false
    
    # 策略1: 正常安装（跳过签名检查）
    if rpm -ivh "$package" --nosignature --nodigest 2>/dev/null; then
        log_info "✅ $package 安装成功"
        install_success=true
    # 策略2: 强制安装（忽略依赖）
    elif rpm -ivh "$package" --force --nodeps --nosignature --nodigest 2>/dev/null; then
        log_info "✅ $package 强制安装成功"
        install_success=true
    # 策略3: 替换安装（如果已有旧版本）
    elif rpm -Uvh "$package" --force --nodeps --nosignature --nodigest 2>/dev/null; then
        log_info "✅ $package 替换安装成功"
        install_success=true
    # 策略4: 忽略所有检查的安装
    elif rpm -ivh "$package" --force --nodeps --nosignature --nodigest --noscripts 2>/dev/null; then
        log_info "✅ $package 完全强制安装成功"
        install_success=true
    fi
    
    if [ "$install_success" = "false" ]; then
        if [ "$required" = "true" ]; then
            log_error "❌ $package 所有安装策略都失败"
            # 显示详细错误信息
            log_error "最后一次安装尝试的错误信息:"
            rpm -ivh "$package" --force --nodeps --nosignature --nodigest
            return 1
        else
            log_warn "⚠️ $package 安装失败，但为可选包，继续..."
            return 0
        fi
    fi
    
    return 0
}

# 安装Docker及其依赖
install_docker() {
    log_step "安装Docker CE及其依赖..."
    
    cd "$SCRIPT_DIR"
    
    # 按正确顺序安装 - 标记每个包是否为必需
    local install_sequence=(
        # 格式: "包名:是否必需(true/false)"
        
        # 基础系统库
        "libcgroup-0.41-21.el7.x86_64.rpm:true"
        "libseccomp-2.3.1-4.el7.x86_64.rpm:true"
        "setools-libs-3.3.8-4.el7.x86_64.rpm:true"
        "python-IPy-0.75-6.el7.noarch.rpm:true"
        
        # SELinux相关依赖
        "libselinux-python-2.5-15.el7.x86_64.rpm:true"
        "libsemanage-python-2.5-14.el7.x86_64.rpm:true"
        "audit-libs-python-2.8.5-4.el7.x86_64.rpm:true"
        "checkpolicy-2.5-8.el7.x86_64.rpm:true"
        
        # 策略管理工具
        "policycoreutils-2.5-34.el7.x86_64.rpm:true"
        "policycoreutils-python-2.5-34.el7.x86_64.rpm:true"
        
        # 容器相关
        "container-selinux-2.119.2-1.911c772.el7_8.noarch.rpm:true"
        "containerd.io-1.6.9-3.1.el7.x86_64.rpm:true"
        
        # Docker主程序
        "docker-ce-cli-26.1.4-1.el7.x86_64.rpm:true"
        "docker-ce-26.1.4-1.el7.x86_64.rpm:true"
        
        # Docker插件
        "docker-compose-plugin-2.21.0-1.el7.x86_64.rpm:true"
        "docker-buildx-plugin-0.11.2-1.el7.x86_64.rpm:true"
    )
    
    local failed_packages=()
    
    for item in "${install_sequence[@]}"; do
        local package="${item%:*}"
        local required="${item#*:}"
        
        if ! install_rpm "$package" "$required"; then
            if [ "$required" = "true" ]; then
                failed_packages+=("$package")
            fi
        fi
    done
    
    if [ ${#failed_packages[@]} -gt 0 ]; then
        log_error "以下必需包安装失败:"
        for pkg in "${failed_packages[@]}"; do
            log_error "  - $pkg"
        done
        return 1
    fi
    
    log_info "Docker CE安装完成"
    
    # 立即重新加载systemd并启动Docker
    systemctl daemon-reload
    groupadd docker 2>/dev/null || true
    systemctl enable docker 2>/dev/null
    systemctl start docker
}



# 验证安装
verify_installation() {
    log_step "验证Docker安装..."
    
    # 检查Docker命令
    if ! command -v docker >/dev/null 2>&1; then
        log_error "❌ Docker命令未找到"
        return 1
    fi
    
    # 验证Docker安装
    sleep 3
    if command -v docker >/dev/null 2>&1 && systemctl is-active --quiet docker; then
        local docker_version=$(docker --version 2>/dev/null || echo "未知版本")
        log_info "Docker版本: $docker_version"
        log_info "✅ Docker服务运行正常"
    else
        log_error "❌ Docker安装或启动失败"
        return 1
    fi
    
    # 验证Docker命令可用性
    if docker info >/dev/null 2>&1; then
        log_info "✅ Docker命令验证成功"
    else
        log_warn "Docker命令验证失败，但服务已启动"
    fi
    
    # 检查Docker Compose插件
    if docker compose version >/dev/null 2>&1; then
        local compose_version=$(docker compose version 2>/dev/null || echo "未知版本")
        log_info "Docker Compose版本: $compose_version"
    else
        log_warn "Docker Compose插件未安装"
    fi
    
    return 0
}



# 显示最终结果
show_result() {
    echo ""
    echo "========================================"
    echo "  Docker CE 安装完成"
    echo "========================================"
    echo ""
    echo "启动Jira服务: cd .. && docker compose up -d"
    echo ""
}

# 主函数
main() {
    check_root
    check_system
    
    # 检查本地RPM包
    if ! check_required_packages; then
        log_error "RPM包检查失败，无法继续安装"
        exit 1
    fi
    
    remove_old_docker
    
    if install_docker; then
        if verify_installation; then
            show_result
            exit 0
        else
            log_error "Docker验证失败"
            exit 1
        fi
    else
        log_error "Docker安装失败"
        exit 1
    fi
}

# 错误处理
trap 'log_error "脚本执行过程中出现错误，退出代码: $?"' ERR

# 运行主函数
main "$@" 