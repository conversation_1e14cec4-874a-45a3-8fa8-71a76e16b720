# Docker CE 离线安装包

这是一个完整的Docker CE离线安装解决方案，专门为CentOS 7系统设计，解决了在纯离线环境下Docker安装的各种问题。

## 版本信息

- **Docker CE**: 26.1.4
- **Containerd**: 1.6.9
- **Docker Compose Plugin**: 2.21.0
- **Docker Buildx Plugin**: 0.11.2
- **支持系统**: CentOS 7.x

## 文件清单

### 核心脚本
- `install-docker-final.sh` - 主安装脚本（增强版）
- `diagnose-docker.sh` - 问题诊断脚本

### RPM包文件
- `libcgroup-0.41-21.el7.x86_64.rpm` - 控制组库
- `libseccomp-2.3.1-4.el7.x86_64.rpm` - 安全计算库
- `setools-libs-3.3.8-4.el7.x86_64.rpm` - SELinux工具库
- `python-IPy-0.75-6.el7.noarch.rpm` - Python IP处理库
- `libselinux-python-2.5-15.el7.x86_64.rpm` - SELinux Python绑定
- `libsemanage-python-2.5-14.el7.x86_64.rpm` - SELinux管理Python绑定
- `audit-libs-python-2.8.5-4.el7.x86_64.rpm` - 审计库Python绑定
- `checkpolicy-2.5-8.el7.x86_64.rpm` - SELinux策略检查工具
- `policycoreutils-2.5-34.el7.x86_64.rpm` - 策略核心工具
- `policycoreutils-python-2.5-34.el7.x86_64.rpm` - 策略工具Python绑定
- `container-selinux-2.119.2-1.911c772.el7_8.noarch.rpm` - 容器SELinux策略
- `containerd.io-1.6.9-3.1.el7.x86_64.rpm` - 容器运行时
- `docker-ce-cli-26.1.4-1.el7.x86_64.rpm` - Docker命令行工具
- `docker-ce-26.1.4-1.el7.x86_64.rpm` - Docker引擎
- `docker-compose-plugin-2.21.0-1.el7.x86_64.rpm` - Docker Compose插件
- `docker-buildx-plugin-0.11.2-1.el7.x86_64.rpm` - Docker Buildx插件

## 主要特性

### 🔧 增强的安装脚本
- **智能依赖检查**: 自动检查所有必需的RPM包
- **多策略安装**: 使用多种安装策略确保包安装成功
- **自动服务配置**: 自动创建和配置systemd服务文件
- **故障自动修复**: 内置故障排除和自动修复机制
- **详细日志记录**: 提供详细的安装过程日志

### 🛠️ 问题诊断工具
- **全面系统检查**: 检查系统状态、服务状态、文件权限等
- **智能问题识别**: 自动识别常见的Docker安装问题
- **修复建议**: 提供针对性的问题修复建议
- **详细报告**: 生成完整的系统诊断报告

### 🚀 自动化修复
- **SELinux处理**: 自动处理SELinux相关问题
- **防火墙配置**: 自动配置防火墙规则
- **权限修复**: 自动修复文件和目录权限
- **服务恢复**: 自动重启和恢复失败的服务

## 使用方法

### 1. 基本安装

```bash
# 进入安装目录
cd docker-offline

# 添加执行权限
chmod +x install-docker-final.sh

# 以root权限运行安装脚本
sudo ./install-docker-final.sh
```

### 2. 问题诊断

如果安装失败或Docker无法正常工作：

```bash
# 运行诊断脚本
chmod +x diagnose-docker.sh
sudo ./diagnose-docker.sh
```

### 3. 手动修复

如果自动修复失败，可以尝试以下手动步骤：

```bash
# 重新加载systemd
sudo systemctl daemon-reload

# 启动Docker服务
sudo systemctl start docker

# 检查服务状态
sudo systemctl status docker

# 查看详细日志
sudo journalctl -u docker -f
```

## 安装过程说明

### 第一阶段：环境检查
1. 检查root权限
2. 验证CentOS 7系统
3. 检查RPM包完整性

### 第二阶段：清理旧版本
1. 卸载旧版本Docker
2. 清理残留文件
3. 重置相关配置

### 第三阶段：依赖安装
1. 按正确顺序安装依赖包
2. 使用多策略确保安装成功
3. 验证每个包的安装状态

### 第四阶段：服务配置
1. 创建systemd服务文件
2. 配置Docker daemon
3. 设置正确的权限

### 第五阶段：服务启动
1. 启动containerd服务
2. 启动Docker服务
3. 验证服务状态

### 第六阶段：安装验证
1. 验证Docker命令
2. 测试Docker API
3. 检查插件功能

## 故障排除

### 常见问题及解决方案

#### 1. "Unit not found" 错误
**原因**: systemd服务文件缺失或损坏
**解决**: 脚本会自动重新创建服务文件

#### 2. Docker服务启动失败
**原因**: 权限问题、SELinux阻止、依赖缺失
**解决**: 
- 检查SELinux状态: `getenforce`
- 临时禁用SELinux: `setenforce 0`
- 检查日志: `journalctl -u docker`

#### 3. Docker命令权限错误
**原因**: 用户不在docker组中
**解决**:
```bash
sudo usermod -aG docker $USER
# 重新登录或运行
newgrp docker
```

#### 4. 容器无法启动
**原因**: 存储驱动问题、磁盘空间不足
**解决**:
- 检查磁盘空间: `df -h`
- 清理Docker数据: `docker system prune`

### 日志查看

```bash
# 查看Docker服务日志
sudo journalctl -u docker -f

# 查看Docker daemon日志
sudo tail -f /var/log/docker.log

# 查看系统启动日志
sudo journalctl -b | grep docker
```

## 验证安装

安装完成后，运行以下命令验证：

```bash
# 检查Docker版本
docker --version

# 检查Docker信息
docker info

# 测试Docker功能
docker run hello-world

# 检查Docker Compose
docker compose version
```

## 性能优化建议

### 1. 存储驱动优化
```bash
# 编辑daemon.json
sudo vi /etc/docker/daemon.json
# 确保使用overlay2存储驱动
```

### 2. 日志管理
```bash
# 配置日志轮转
# 在daemon.json中设置log-opts
```

### 3. 网络优化
```bash
# 配置Docker网络
# 避免与现有网络冲突
```

## 安全建议

1. **定期更新**: 定期更新Docker到最新版本
2. **权限控制**: 严格控制docker组成员
3. **网络安全**: 配置适当的防火墙规则
4. **镜像安全**: 使用可信的镜像源
5. **日志监控**: 监控Docker相关日志

## 技术支持

如果遇到问题：

1. 首先运行诊断脚本: `./diagnose-docker.sh`
2. 查看详细日志: `journalctl -u docker`
3. 检查系统资源: `free -h && df -h`
4. 重新运行安装脚本

## 更新日志

### v3.0 (当前版本)
- ✅ 增强的错误处理和自动修复
- ✅ 完整的systemd服务文件管理
- ✅ 智能故障诊断和修复
- ✅ 详细的安装过程日志
- ✅ 多策略包安装机制

### v2.0
- ✅ 基础离线安装功能
- ✅ RPM包依赖管理
- ✅ 基本错误处理

### v1.0
- ✅ 初始版本
- ✅ 基本安装功能
