[mysqld]
# JIRA 8.13.7 优化配置
default-authentication-plugin = mysql_native_password
character-set-server = utf8mb4
collation-server = utf8mb4_bin

# JIRA specific settings
sql_mode = NO_AUTO_VALUE_ON_ZERO
innodb_default_row_format = DYNAMIC
innodb_large_prefix = 1
innodb_file_format = Barracuda
max_allowed_packet = 256M
innodb_log_file_size = 2G
max_connections = 100
innodb_buffer_pool_size = 512M

[mysql]
default-character-set = utf8mb4

[client]
default-character-set = utf8mb4