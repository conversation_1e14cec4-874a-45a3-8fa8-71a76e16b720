[mysqld]
default-storage-engine = INNODB
character-set-server = utf8mb4
collation-server = utf8mb4_bin
transaction-isolation = READ-COMMITTED

# JIRA MySQL 5.7 specific settings
innodb_default_row_format = DYNAMIC
innodb_large_prefix = ON
innodb_file_format = Barracuda
innodb_log_file_size = 2G
max_allowed_packet = 256M
max_connections = 100
innodb_buffer_pool_size = 512M

[mysql]
default-character-set = utf8mb4

[client]
default-character-set = utf8mb4