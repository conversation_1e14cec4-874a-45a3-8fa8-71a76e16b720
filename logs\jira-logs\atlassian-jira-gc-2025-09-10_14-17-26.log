[2025-09-10T14:17:26.309+0800][0.019s][info][gc,heap] Heap region size: 1M
[2025-09-10T14:17:26.320+0800][0.030s][info][gc     ] Using G1
[2025-09-10T14:17:26.322+0800][0.032s][info][gc,heap,coops] Heap address: 0x0000000080000000, size: 2048 MB, Compressed Oops mode: 32-bit
[2025-09-10T14:17:26.322+0800][0.033s][info][gc,cds       ] Mark closed archive regions in map: [0x00000000ffe00000, 0x00000000ffe6bff8]
[2025-09-10T14:17:26.324+0800][0.034s][info][gc,cds       ] Mark open archive regions in map: [0x00000000ffc00000, 0x00000000ffc46ff8]
[2025-09-10T14:17:27.257+0800][0.969s][info][gc,start     ] GC(0) Pa<PERSON> Young (Normal) (G1 Evacuation Pause)
[2025-09-10T14:17:27.262+0800][0.974s][info][gc,task      ] GC(0) Using 15 workers of 15 for evacuation
[2025-09-10T14:17:27.272+0800][0.983s][info][gc,phases    ] GC(0)   Pre Evacuate Collection Set: 0.0ms
[2025-09-10T14:17:27.276+0800][0.987s][info][gc,phases    ] GC(0)   Evacuate Collection Set: 4.2ms
[2025-09-10T14:17:27.276+0800][0.988s][info][gc,phases    ] GC(0)   Post Evacuate Collection Set: 3.6ms
[2025-09-10T14:17:27.277+0800][0.989s][info][gc,phases    ] GC(0)   Other: 6.8ms
[2025-09-10T14:17:27.278+0800][0.989s][info][gc,heap      ] GC(0) Eden regions: 51->0(46)
[2025-09-10T14:17:27.278+0800][0.990s][info][gc,heap      ] GC(0) Survivor regions: 0->7(7)
[2025-09-10T14:17:27.278+0800][0.990s][info][gc,heap      ] GC(0) Old regions: 2->2
[2025-09-10T14:17:27.280+0800][0.991s][info][gc,heap      ] GC(0) Humongous regions: 1->0
[2025-09-10T14:17:27.282+0800][0.993s][info][gc,metaspace ] GC(0) Metaspace: 10800K(11648K)->10800K(11648K) NonClass: 9695K(10240K)->9695K(10240K) Class: 1105K(1408K)->1105K(1408K)
[2025-09-10T14:17:27.282+0800][0.994s][info][gc           ] GC(0) Pause Young (Normal) (G1 Evacuation Pause) 52M->7M(1026M) 25.375ms
[2025-09-10T14:17:27.283+0800][0.994s][info][gc,cpu       ] GC(0) User=0.12s Sys=0.00s Real=0.03s
[2025-09-10T14:17:27.500+0800][1.212s][info][gc,start     ] GC(1) Pause Young (Normal) (G1 Evacuation Pause)
[2025-09-10T14:17:27.500+0800][1.212s][info][gc,task      ] GC(1) Using 15 workers of 15 for evacuation
[2025-09-10T14:17:27.508+0800][1.220s][info][gc,phases    ] GC(1)   Pre Evacuate Collection Set: 0.1ms
[2025-09-10T14:17:27.509+0800][1.220s][info][gc,phases    ] GC(1)   Evacuate Collection Set: 4.0ms
[2025-09-10T14:17:27.509+0800][1.220s][info][gc,phases    ] GC(1)   Post Evacuate Collection Set: 3.4ms
[2025-09-10T14:17:27.509+0800][1.221s][info][gc,phases    ] GC(1)   Other: 0.7ms
[2025-09-10T14:17:27.509+0800][1.221s][info][gc,heap      ] GC(1) Eden regions: 46->0(59)
[2025-09-10T14:17:27.510+0800][1.221s][info][gc,heap      ] GC(1) Survivor regions: 7->7(7)
[2025-09-10T14:17:27.510+0800][1.221s][info][gc,heap      ] GC(1) Old regions: 2->13
[2025-09-10T14:17:27.510+0800][1.222s][info][gc,heap      ] GC(1) Humongous regions: 1->1
[2025-09-10T14:17:27.510+0800][1.222s][info][gc,metaspace ] GC(1) Metaspace: 11909K(12800K)->11909K(12800K) NonClass: 10679K(11264K)->10679K(11264K) Class: 1230K(1536K)->1230K(1536K)
[2025-09-10T14:17:27.510+0800][1.222s][info][gc           ] GC(1) Pause Young (Normal) (G1 Evacuation Pause) 54M->18M(1026M) 10.211ms
[2025-09-10T14:17:27.511+0800][1.222s][info][gc,cpu       ] GC(1) User=0.08s Sys=0.03s Real=0.01s
[2025-09-10T14:17:27.903+0800][1.615s][info][gc,start     ] GC(2) Pause Young (Normal) (G1 Evacuation Pause)
[2025-09-10T14:17:27.904+0800][1.615s][info][gc,task      ] GC(2) Using 15 workers of 15 for evacuation
[2025-09-10T14:17:27.910+0800][1.621s][info][gc,phases    ] GC(2)   Pre Evacuate Collection Set: 0.0ms
[2025-09-10T14:17:27.911+0800][1.622s][info][gc,phases    ] GC(2)   Evacuate Collection Set: 2.1ms
[2025-09-10T14:17:27.911+0800][1.623s][info][gc,phases    ] GC(2)   Post Evacuate Collection Set: 3.2ms
[2025-09-10T14:17:27.911+0800][1.623s][info][gc,phases    ] GC(2)   Other: 1.0ms
[2025-09-10T14:17:27.912+0800][1.623s][info][gc,heap      ] GC(2) Eden regions: 59->0(80)
[2025-09-10T14:17:27.912+0800][1.623s][info][gc,heap      ] GC(2) Survivor regions: 7->3(9)
[2025-09-10T14:17:27.912+0800][1.624s][info][gc,heap      ] GC(2) Old regions: 13->20
[2025-09-10T14:17:27.912+0800][1.624s][info][gc,heap      ] GC(2) Humongous regions: 1->1
[2025-09-10T14:17:27.912+0800][1.624s][info][gc,metaspace ] GC(2) Metaspace: 12997K(13952K)->12997K(13952K) NonClass: 11674K(12288K)->11674K(12288K) Class: 1322K(1664K)->1322K(1664K)
[2025-09-10T14:17:27.913+0800][1.624s][info][gc           ] GC(2) Pause Young (Normal) (G1 Evacuation Pause) 77M->21M(1026M) 9.474ms
[2025-09-10T14:17:27.913+0800][1.625s][info][gc,cpu       ] GC(2) User=0.05s Sys=0.02s Real=0.00s
[2025-09-10T14:17:28.241+0800][1.953s][info][gc,start     ] GC(3) Pause Young (Normal) (G1 Evacuation Pause)
[2025-09-10T14:17:28.242+0800][1.954s][info][gc,task      ] GC(3) Using 15 workers of 15 for evacuation
[2025-09-10T14:17:28.247+0800][1.959s][info][gc,phases    ] GC(3)   Pre Evacuate Collection Set: 0.0ms
[2025-09-10T14:17:28.247+0800][1.959s][info][gc,phases    ] GC(3)   Evacuate Collection Set: 1.0ms
[2025-09-10T14:17:28.247+0800][1.959s][info][gc,phases    ] GC(3)   Post Evacuate Collection Set: 1.9ms
[2025-09-10T14:17:28.247+0800][1.959s][info][gc,phases    ] GC(3)   Other: 2.7ms
[2025-09-10T14:17:28.248+0800][1.959s][info][gc,heap      ] GC(3) Eden regions: 80->0(112)
[2025-09-10T14:17:28.248+0800][1.959s][info][gc,heap      ] GC(3) Survivor regions: 3->4(11)
[2025-09-10T14:17:28.248+0800][1.959s][info][gc,heap      ] GC(3) Old regions: 20->20
[2025-09-10T14:17:28.248+0800][1.960s][info][gc,heap      ] GC(3) Humongous regions: 1->1
[2025-09-10T14:17:28.248+0800][1.960s][info][gc,metaspace ] GC(3) Metaspace: 13063K(13952K)->13063K(13952K) NonClass: 11735K(12288K)->11735K(12288K) Class: 1327K(1664K)->1327K(1664K)
[2025-09-10T14:17:28.248+0800][1.960s][info][gc           ] GC(3) Pause Young (Normal) (G1 Evacuation Pause) 101M->22M(1026M) 6.939ms
[2025-09-10T14:17:28.248+0800][1.960s][info][gc,cpu       ] GC(3) User=0.04s Sys=0.00s Real=0.01s
