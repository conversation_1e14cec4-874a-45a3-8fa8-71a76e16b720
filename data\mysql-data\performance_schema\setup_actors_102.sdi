{"mysqld_version_id": 80035, "dd_version": 80023, "sdi_version": 80019, "dd_object_type": "Table", "dd_object": {"name": "setup_actors", "mysql_version_id": 80035, "created": 20250910051727, "last_altered": 20250910051727, "hidden": 1, "options": "avg_row_length=0;key_block_size=0;keys_disabled=0;pack_record=0;server_p_s_table=1;stats_auto_recalc=0;stats_sample_pages=0;", "columns": [{"name": "HOST", "type": 29, "is_nullable": false, "is_zerofill": false, "is_unsigned": false, "is_auto_increment": false, "is_virtual": false, "hidden": 1, "ordinal_position": 1, "char_length": 255, "numeric_precision": 0, "numeric_scale": 0, "numeric_scale_null": true, "datetime_precision": 0, "datetime_precision_null": 1, "has_no_default": false, "default_value_null": false, "srs_id_null": true, "srs_id": 0, "default_value": "JSAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAg\nICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAg\nICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAg\nICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAg\nICAgICAgICAgICAgICAgICAgICAgICAgICAg", "default_value_utf8_null": false, "default_value_utf8": "%", "default_option": "", "update_option": "", "comment": "", "generation_expression": "", "generation_expression_utf8": "", "options": "interval_count=0;", "se_private_data": "", "engine_attribute": "", "secondary_engine_attribute": "", "column_key": 2, "column_type_utf8": "char(255)", "elements": [], "collation_id": 11, "is_explicit_collation": true}, {"name": "USER", "type": 29, "is_nullable": false, "is_zerofill": false, "is_unsigned": false, "is_auto_increment": false, "is_virtual": false, "hidden": 1, "ordinal_position": 2, "char_length": 128, "numeric_precision": 0, "numeric_scale": 0, "numeric_scale_null": true, "datetime_precision": 0, "datetime_precision_null": 1, "has_no_default": false, "default_value_null": false, "srs_id_null": true, "srs_id": 0, "default_value": "JSAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAg\nICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAg\nICAgICAgICAgICAgICA=", "default_value_utf8_null": false, "default_value_utf8": "%", "default_option": "", "update_option": "", "comment": "", "generation_expression": "", "generation_expression_utf8": "", "options": "interval_count=0;", "se_private_data": "", "engine_attribute": "", "secondary_engine_attribute": "", "column_key": 2, "column_type_utf8": "char(32)", "elements": [], "collation_id": 46, "is_explicit_collation": true}, {"name": "ROLE", "type": 29, "is_nullable": false, "is_zerofill": false, "is_unsigned": false, "is_auto_increment": false, "is_virtual": false, "hidden": 1, "ordinal_position": 3, "char_length": 128, "numeric_precision": 0, "numeric_scale": 0, "numeric_scale_null": true, "datetime_precision": 0, "datetime_precision_null": 1, "has_no_default": false, "default_value_null": false, "srs_id_null": true, "srs_id": 0, "default_value": "JSAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAg\nICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAg\nICAgICAgICAgICAgICA=", "default_value_utf8_null": false, "default_value_utf8": "%", "default_option": "", "update_option": "", "comment": "", "generation_expression": "", "generation_expression_utf8": "", "options": "interval_count=0;", "se_private_data": "", "engine_attribute": "", "secondary_engine_attribute": "", "column_key": 2, "column_type_utf8": "char(32)", "elements": [], "collation_id": 46, "is_explicit_collation": true}, {"name": "ENABLED", "type": 22, "is_nullable": false, "is_zerofill": false, "is_unsigned": false, "is_auto_increment": false, "is_virtual": false, "hidden": 1, "ordinal_position": 4, "char_length": 12, "numeric_precision": 0, "numeric_scale": 0, "numeric_scale_null": true, "datetime_precision": 0, "datetime_precision_null": 1, "has_no_default": false, "default_value_null": false, "srs_id_null": true, "srs_id": 0, "default_value": "AQ==", "default_value_utf8_null": false, "default_value_utf8": "YES", "default_option": "", "update_option": "", "comment": "", "generation_expression": "", "generation_expression_utf8": "", "options": "interval_count=2;", "se_private_data": "", "engine_attribute": "", "secondary_engine_attribute": "", "column_key": 1, "column_type_utf8": "enum('YES','NO')", "elements": [{"name": "WUVT", "index": 1}, {"name": "Tk8=", "index": 2}], "collation_id": 255, "is_explicit_collation": false}, {"name": "HISTORY", "type": 22, "is_nullable": false, "is_zerofill": false, "is_unsigned": false, "is_auto_increment": false, "is_virtual": false, "hidden": 1, "ordinal_position": 5, "char_length": 12, "numeric_precision": 0, "numeric_scale": 0, "numeric_scale_null": true, "datetime_precision": 0, "datetime_precision_null": 1, "has_no_default": false, "default_value_null": false, "srs_id_null": true, "srs_id": 0, "default_value": "AQ==", "default_value_utf8_null": false, "default_value_utf8": "YES", "default_option": "", "update_option": "", "comment": "", "generation_expression": "", "generation_expression_utf8": "", "options": "interval_count=2;", "se_private_data": "", "engine_attribute": "", "secondary_engine_attribute": "", "column_key": 1, "column_type_utf8": "enum('YES','NO')", "elements": [{"name": "WUVT", "index": 1}, {"name": "Tk8=", "index": 2}], "collation_id": 255, "is_explicit_collation": false}], "schema_ref": "performance_schema", "se_private_id": 18446744073709551615, "engine": "PERFORMANCE_SCHEMA", "last_checked_for_upgrade_version_id": 0, "comment": "", "se_private_data": "", "engine_attribute": "", "secondary_engine_attribute": "", "row_format": 1, "partition_type": 0, "partition_expression": "", "partition_expression_utf8": "", "default_partitioning": 0, "subpartition_type": 0, "subpartition_expression": "", "subpartition_expression_utf8": "", "default_subpartitioning": 0, "indexes": [{"name": "PRIMARY", "hidden": false, "is_generated": false, "ordinal_position": 1, "comment": "", "options": "flags=0;", "se_private_data": "", "type": 1, "algorithm": 4, "is_algorithm_explicit": false, "is_visible": true, "engine": "PERFORMANCE_SCHEMA", "engine_attribute": "", "secondary_engine_attribute": "", "elements": [{"ordinal_position": 1, "length": 255, "order": 1, "hidden": false, "column_opx": 0}, {"ordinal_position": 2, "length": 128, "order": 1, "hidden": false, "column_opx": 1}, {"ordinal_position": 3, "length": 128, "order": 1, "hidden": false, "column_opx": 2}]}], "foreign_keys": [], "check_constraints": [], "partitions": [], "collation_id": 255}}