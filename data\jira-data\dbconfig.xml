<?xml version="1.0" encoding="UTF-8"?>

<jira-database-config>
  <name>defaultDS</name>
  <delegator-name>default</delegator-name>
  
  <schema-name></schema-name>
  <database-type>mysql8</database-type>
  <jdbc-datasource>
    <url>*******************************************************************************************************************************************************************************************</url>
    <username>root</username>
    <password>iie.ac.cn</password>
    <driver-class>com.mysql.cj.jdbc.Driver</driver-class>
    

    <pool-min-size>20</pool-min-size>
    <pool-max-size>100</pool-max-size>
    <pool-min-idle>10</pool-min-idle>
    <pool-max-idle>20</pool-max-idle>

    <pool-max-wait>30000</pool-max-wait>
    <validation-query>select 1</validation-query>
    
      <validation-query-timeout>3</validation-query-timeout>
    
    <time-between-eviction-runs-millis>30000</time-between-eviction-runs-millis>
    <min-evictable-idle-time-millis>5000</min-evictable-idle-time-millis>
    <pool-remove-abandoned>true</pool-remove-abandoned>
    <pool-remove-abandoned-timeout>300</pool-remove-abandoned-timeout>
    <pool-test-while-idle>true</pool-test-while-idle>
    <pool-test-on-borrow>false</pool-test-on-borrow>
  </jdbc-datasource>
</jira-database-config>